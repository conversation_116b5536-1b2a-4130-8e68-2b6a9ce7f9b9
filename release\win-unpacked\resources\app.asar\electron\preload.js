const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Translation functions
    translateText: (text) => ipcRenderer.invoke('translate-text', text),
    getSuggestions: (text, maxSuggestions) => ipcRenderer.invoke('get-suggestions', text, maxSuggestions),
    transliterateWithContext: (text, context) => ipcRenderer.invoke('transliterate-with-context', text, context),
    isWordInDictionary: (word) => ipcRenderer.invoke('is-word-in-dictionary', word),
    
    // Menu event listeners
    onMenuNew: (callback) => ipcRenderer.on('menu-new', callback),
    onMenuOpen: (callback) => ipcRenderer.on('menu-open', callback),
    onMenuSave: (callback) => ipcRenderer.on('menu-save', callback),
    
    // Utility functions
    platform: process.platform,
    versions: process.versions
});

// Expose clipboard API
contextBridge.exposeInMainWorld('clipboardAPI', {
    writeText: (text) => {
        return navigator.clipboard.writeText(text);
    },
    readText: () => {
        return navigator.clipboard.readText();
    }
});

// Expose file system operations (limited and secure)
contextBridge.exposeInMainWorld('fileAPI', {
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
    writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content)
});
