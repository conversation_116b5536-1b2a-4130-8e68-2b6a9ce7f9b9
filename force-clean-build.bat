@echo off
echo ========================================
echo Tamil Transliterator - FORCE Clean Build
echo ========================================
echo.

echo This will aggressively fix file lock issues...
echo.

echo [1/8] Killing ALL related processes...
taskkill /f /im "Tamil Transliterator.exe" 2>nul
taskkill /f /im "electron.exe" 2>nul
taskkill /f /im "app-builder.exe" 2>nul
taskkill /f /im "node.exe" 2>nul
taskkill /f /im "npm.exe" 2>nul
echo All processes terminated

echo [2/8] Waiting for Windows to release file handles...
timeout /t 5 /nobreak > nul

echo [3/8] Using PowerShell to force unlock files...
powershell -Command "Get-Process | Where-Object {$_.ProcessName -like '*electron*' -or $_.ProcessName -like '*app-builder*'} | Stop-Process -Force -ErrorAction SilentlyContinue"

echo [4/8] Attempting to unlock specific file...
if exist "dist\win-unpacked\resources\app.asar" (
    echo Found locked file, attempting to unlock...
    powershell -Command "Remove-Item -Path 'dist\win-unpacked\resources\app.asar' -Force -ErrorAction SilentlyContinue"
)

echo [5/8] Removing entire dist folder with multiple methods...
if exist dist (
    echo Method 1: Standard removal...
    rmdir /s /q dist 2>nul
    
    if exist dist (
        echo Method 2: PowerShell removal...
        powershell -Command "Remove-Item -Path 'dist' -Recurse -Force -ErrorAction SilentlyContinue"
        timeout /t 2 /nobreak > nul
    )
    
    if exist dist (
        echo Method 3: Renaming and removing...
        ren dist dist_old_%RANDOM% 2>nul
        timeout /t 1 /nobreak > nul
        for /d %%i in (dist_old_*) do rmdir /s /q "%%i" 2>nul
    )
)

echo [6/8] Cleaning npm cache and temp files...
call npm cache clean --force 2>nul
if exist node_modules\.cache rmdir /s /q node_modules\.cache 2>nul
if exist %TEMP%\electron-* rmdir /s /q %TEMP%\electron-* 2>nul

echo [7/8] Final wait before rebuild...
timeout /t 3 /nobreak > nul

echo [8/8] Building with fresh start...
echo This may take several minutes...
echo.

call npm run build-electron-win
if errorlevel 1 (
    echo.
    echo ❌ BUILD STILL FAILED
    echo.
    echo LAST RESORT SOLUTIONS:
    echo.
    echo 1. RESTART YOUR COMPUTER
    echo    - This will clear ALL file locks
    echo    - Then run: npm run build-electron-win
    echo.
    echo 2. BUILD IN NEW LOCATION
    echo    - Copy project to different folder
    echo    - Build from there
    echo.
    echo 3. USE DIFFERENT OUTPUT FOLDER
    echo    - Edit package.json build.directories.output
    echo    - Change from "dist" to "release"
    echo.
    echo 4. DISABLE ANTIVIRUS TEMPORARILY
    echo    - Some antivirus software locks files
    echo    - Disable real-time protection during build
    echo.
    echo Press any key to try alternative build method...
    pause > nul
    
    echo.
    echo Trying alternative build with different output...
    if not exist release mkdir release
    call npx electron-builder --win --dir --config.directories.output=release
    
    if errorlevel 1 (
        echo.
        echo ❌ ALTERNATIVE BUILD ALSO FAILED
        echo.
        echo PLEASE RESTART YOUR COMPUTER AND TRY AGAIN
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo ✅ BUILD SUCCESSFUL!
echo ========================================
echo.

if exist "dist\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer: dist\Tamil Transliterator Setup 1.0.0.exe
) else if exist "release\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer: release\Tamil Transliterator Setup 1.0.0.exe
)

if exist "dist\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable: dist\win-unpacked\Tamil Transliterator.exe
) else if exist "release\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable: release\win-unpacked\Tamil Transliterator.exe
)

echo.
echo 🚀 Your .exe files are ready for distribution!
echo.

if exist dist (
    start explorer "dist"
) else if exist release (
    start explorer "release"
)

pause
