@echo off
echo ========================================
echo Tamil Transliterator Desktop - Clean
echo ========================================
echo.

echo Cleaning build artifacts and temporary files...
echo.

echo [1/4] Cleaning dist folder...
if exist dist (
    rmdir /s /q dist 2>nul
    echo ✓ Dist folder removed
) else (
    echo ✓ Dist folder already clean
)

echo [2/4] Cleaning native build...
if exist "native\build" (
    cd native
    rmdir /s /q build 2>nul
    cd ..
    echo ✓ Native build folder removed
) else (
    echo ✓ Native build already clean
)

echo [3/4] Cleaning node_modules cache...
if exist "node_modules\.cache" (
    rmdir /s /q "node_modules\.cache" 2>nul
    echo ✓ Node modules cache removed
) else (
    echo ✓ Node modules cache already clean
)

echo [4/4] Cleaning temporary files...
del /q nul 2>nul
del /q *.tmp 2>nul
del /q *.log 2>nul
echo ✓ Temporary files removed

echo.
echo ========================================
echo ✅ CLEANUP COMPLETED!
echo ========================================
echo.
echo Cleaned:
echo - Build artifacts (dist/, native/build/)
echo - Cache files
echo - Temporary files
echo.
echo Ready for fresh build!
echo.
pause
