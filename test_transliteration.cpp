#include <iostream>
#include "cpp/TranslateModule.h"

int main() {
    std::cout << "Testing Tamil Transliteration Integration" << std::endl;
    std::cout << "==========================================" << std::endl;
    
    // Test basic transliteration
    std::string test1 = "vanakkam";
    std::string result1 = TranslateModule::translateText(test1);
    std::cout << "Input: " << test1 << " -> Output: " << result1 << std::endl;
    
    std::string test2 = "tamil";
    std::string result2 = TranslateModule::translateText(test2);
    std::cout << "Input: " << test2 << " -> Output: " << result2 << std::endl;
    
    std::string test3 = "nandri";
    std::string result3 = TranslateModule::translateText(test3);
    std::cout << "Input: " << test3 << " -> Output: " << result3 << std::endl;
    
    std::string test4 = "hello";
    std::string result4 = TranslateModule::translateText(test4);
    std::cout << "Input: " << test4 << " -> Output: " << result4 << std::endl;
    
    // Test suggestions
    std::cout << "\nTesting suggestions for 'van':" << std::endl;
    auto suggestions = TranslateModule::getSuggestions("van", 3);
    for (size_t i = 0; i < suggestions.size(); ++i) {
        std::cout << "  " << (i+1) << ". " << suggestions[i] << std::endl;
    }
    
    // Test dictionary lookup
    std::cout << "\nDictionary tests:" << std::endl;
    std::cout << "Is 'vanakkam' in dictionary? " << (TranslateModule::isWordInDictionary("vanakkam") ? "Yes" : "No") << std::endl;
    std::cout << "Is 'xyz123' in dictionary? " << (TranslateModule::isWordInDictionary("xyz123") ? "Yes" : "No") << std::endl;
    
    return 0;
}
