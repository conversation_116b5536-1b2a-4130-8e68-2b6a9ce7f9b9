@echo off
echo ========================================
echo Tamil Transliterator - Simple Build Fix
echo ========================================
echo.

echo This will build a simplified version that should work
echo.

echo [1/5] Cleaning everything...
cd native
if exist build rmdir /s /q build
if exist node_modules rmdir /s /q node_modules
cd ..

echo [2/5] Installing dependencies...
cd native
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    cd ..
    pause
    exit /b 1
)

echo [3/5] Building with simplified addon...
call npx node-gyp rebuild
if errorlevel 1 (
    echo ERROR: Build failed
    echo.
    echo The simplified version still failed to build.
    echo This might be due to:
    echo 1. Missing Visual Studio Build Tools
    echo 2. Missing Windows SDK
    echo 3. Node.js version compatibility issues
    echo.
    echo Try installing Visual Studio Community 2022 with C++ tools
    cd ..
    pause
    exit /b 1
)
cd ..

echo [4/5] Testing the build...
if exist "native\build\Release\transliterate_addon.node" (
    echo ✓ Native module built successfully!
    echo.
    echo Testing basic functionality...
    node test-native.js
    if errorlevel 1 (
        echo WARNING: Test failed, but module exists
    ) else (
        echo ✓ Test passed!
    )
) else (
    echo ❌ Build output not found
    cd ..
    pause
    exit /b 1
)

echo [5/5] Starting Electron app...
echo.
echo The app will start now. Test these words:
echo - vanakkam (should show Tamil text)
echo - nandri (should show Tamil text)
echo - tamil (should show Tamil text)
echo.
echo If you see Tamil text, the backend is working!
echo If you see English text unchanged, there might still be issues.
echo.

timeout /t 3 /nobreak > nul
start npm run electron-dev

echo ========================================
echo Build completed!
echo ========================================
echo.
echo What was simplified:
echo - Removed complex N-API wrapper functions
echo - Simplified module initialization
echo - Reduced to core translateText and getSuggestions only
echo - Better error handling
echo.
echo If translation still doesn't work:
echo 1. Check Electron DevTools console (F12)
echo 2. Look for error messages
echo 3. The fallback system should still provide basic translation
echo.
pause
