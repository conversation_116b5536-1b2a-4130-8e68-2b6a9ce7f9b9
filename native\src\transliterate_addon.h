#ifndef TRANSLITERATE_ADDON_H
#define TRANSLITERATE_ADDON_H

#include <napi.h>
#include <string>
#include <vector>
#include "TranslateModule.h"

namespace TransliterateAddon {
    // Function declarations for Node.js bindings
    Napi::String TranslateText(const Napi::CallbackInfo& info);
    Napi::Array GetSuggestions(const Napi::CallbackInfo& info);
    Napi::String TransliterateWithContext(const Napi::CallbackInfo& info);
    Napi::Boolean IsWordInDictionary(const Napi::CallbackInfo& info);
    
    // Module initialization
    Napi::Object Init(Napi::Env env, Napi::Object exports);
}

#endif // TRANSLITERATE_ADDON_H
