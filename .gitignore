# Tamil Transliterator - Multi-Platform Application
# ================================================

# Operating System Files
# ----------------------
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
*~
.directory
.Trash-*

# Node.js Dependencies
# -------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.eslintcache
.yarn-integrity
*.tgz
*.tar.gz

# React Native & Metro
# -------------------
*.jsbundle
.metro-health-check*
metro.config.local.js

# Android Development
# ------------------
# Build outputs
android/app/build/
android/build/
android/.gradle/
android/app/release/
android/app/debug/
*.keystore
!android/app/debug.keystore

# Android Studio / IntelliJ
.idea/
*.iml
.gradle/
local.properties
.cxx/
*.hprof
.kotlin/
captures/

# NDK & C++ Build Files
# --------------------
android/app/.cxx/
android/app/build/intermediates/cxx/
*.so
*.a
*.o
obj/
libs/

# Desktop (Electron) Build Files
# ------------------------------
dist/
release/windows/win-unpacked/
release/android/*.apk
*.exe
*.dmg
*.deb
*.rpm
native/build/
native/node_modules/
*.node

# Environment & Configuration
# ---------------------------
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
# -------
/coverage
*.lcov
.nyc_output

# Logs
# ----
logs/
*.log

# Temporary Files
# --------------
*.tmp
*.temp
.cache/
.vscode/
.history/

# Development Tools
# ----------------
# Flipper
*.flipper

# Debugging
android/app/src/main/assets/index.android.bundle
android/app/src/main/assets/index.android.bundle.meta
android/app/src/main/res/drawable-*
android/app/src/main/res/raw/

# Package Managers
# ---------------
package-lock.json
yarn.lock
.pnp
.pnp.js

# Editor Backups
# --------------
*~
*.swp
*.swo
*#
.#*
