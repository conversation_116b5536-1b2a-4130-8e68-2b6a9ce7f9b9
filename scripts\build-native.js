const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Building native module...');

const nativeDir = path.join(__dirname, '..', 'native');
const originalCwd = process.cwd();

try {
    // Change to native directory
    process.chdir(nativeDir);
    
    // Check if package.json exists
    if (!fs.existsSync('package.json')) {
        console.error('Native module package.json not found!');
        process.exit(1);
    }
    
    // Install dependencies if node_modules doesn't exist
    if (!fs.existsSync('node_modules')) {
        console.log('Installing native module dependencies...');
        execSync('npm install', { stdio: 'inherit' });
    }
    
    // Clean previous build
    console.log('Cleaning previous build...');
    try {
        execSync('npm run clean', { stdio: 'inherit' });
    } catch (e) {
        // Ignore clean errors
        console.log('Clean completed (or no previous build found)');
    }

    // Also manually clean build directory
    const buildDir = path.join(nativeDir, 'build');
    if (fs.existsSync(buildDir)) {
        console.log('Removing build directory...');
        fs.rmSync(buildDir, { recursive: true, force: true });
    }
    
    // Configure and build
    console.log('Configuring native module...');
    execSync('npm run configure', { stdio: 'inherit' });
    
    console.log('Building native module...');
    execSync('npm run build', { stdio: 'inherit' });
    
    // Verify build output
    const buildPath = path.join(nativeDir, 'build', 'Release', 'transliterate_addon.node');
    if (fs.existsSync(buildPath)) {
        console.log('✅ Native module built successfully!');
        console.log(`📁 Output: ${buildPath}`);
    } else {
        console.error('❌ Native module build failed - output file not found');
        process.exit(1);
    }
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
} finally {
    // Restore original working directory
    process.chdir(originalCwd);
}

console.log('Native module build completed!');
