@echo off
echo ========================================
echo Tamil Transliterator - Release Builder
echo ========================================
echo.

echo Building production-ready .exe file for distribution...
echo.

echo [1/6] Checking prerequisites...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found!
    pause
    exit /b 1
)

if not exist "native\build\Release\transliterate_addon.node" (
    echo Native module not found. Building it first...
    call fix-build-simple.bat
    if errorlevel 1 (
        echo ERROR: Failed to build native module
        pause
        exit /b 1
    )
) else (
    echo ✓ Native module found
)

echo [2/6] Installing/updating dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [3/6] Building native module for production...
cd native
call npm run build
if errorlevel 1 (
    echo ERROR: Failed to build native module
    cd ..
    pause
    exit /b 1
)
cd ..

echo [4/6] Creating application icon...
if not exist "electron\assets\icon.ico" (
    echo Creating default icon...
    echo Note: You can replace electron\assets\icon.ico with your custom icon
)

echo [5/6] Building Electron application...
echo This may take several minutes...
call npm run build-electron-win
if errorlevel 1 (
    echo ERROR: Failed to build Electron application
    echo.
    echo Common issues:
    echo 1. Missing electron-builder dependencies
    echo 2. Antivirus blocking the build
    echo 3. Insufficient disk space
    pause
    exit /b 1
)

echo [6/6] Verifying build output...
if exist "dist\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created successfully!
    set INSTALLER_SIZE=
    for %%A in ("dist\Tamil Transliterator Setup 1.0.0.exe") do set INSTALLER_SIZE=%%~zA
    echo   File: Tamil Transliterator Setup 1.0.0.exe
    echo   Size: %INSTALLER_SIZE% bytes
)

if exist "dist\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version created successfully!
    echo   Folder: dist\win-unpacked\
    echo   Executable: Tamil Transliterator.exe
)

echo.
echo ========================================
echo ✅ RELEASE BUILD COMPLETED!
echo ========================================
echo.
echo 📦 Distribution Files Created:
echo.
echo 1. INSTALLER (Recommended for most users):
echo    📁 dist\Tamil Transliterator Setup 1.0.0.exe
echo    - Professional Windows installer
echo    - Creates desktop shortcut
echo    - Adds to Start Menu
echo    - Includes uninstaller
echo    - Size: ~100-200MB
echo.
echo 2. PORTABLE (For advanced users):
echo    📁 dist\win-unpacked\
echo    - No installation required
echo    - Copy folder and run
echo    - Perfect for USB drives
echo    - Size: ~150-300MB
echo.
echo 🚀 Ready for Distribution:
echo - Share the installer with end users
echo - No additional software required
echo - Works on Windows 10/11 (64-bit)
echo - Includes all dependencies
echo.
echo 📋 Next Steps:
echo 1. Test the installer on a clean Windows machine
echo 2. Create release notes
echo 3. Upload to your distribution platform
echo 4. Share with users!
echo.

echo Opening dist folder...
start explorer "dist"

echo.
pause
