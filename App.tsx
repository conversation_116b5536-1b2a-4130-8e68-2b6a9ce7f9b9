import React, { useState, useEffect, useRef } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  View,
  useColorScheme,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import TranslateModule from './src/TranslateModule';

function App(): React.JSX.Element {
  const [inputText, setInputText] = useState('');
  const [translatedText, setTranslatedText] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<TextInput>(null);
  const isDarkMode = useColorScheme() === 'dark';

  const backgroundStyle = {
    backgroundColor: isDarkMode ? '#000000' : '#ffffff',
    flex: 1,
  };

  const textStyle = {
    color: isDarkMode ? '#ffffff' : '#000000',
  };

  const placeholderColor = isDarkMode ? '#888888' : '#666666';

  // Handle translation with suggestions
  useEffect(() => {
    const translateText = async () => {
      if (inputText.trim() === '') {
        setTranslatedText('');
        setSuggestions([]);
        return;
      }

      try {
        const result = await TranslateModule.translateText(inputText);
        setTranslatedText(result.translatedText);
          // Get additional suggestions if the new methods are available
        try {
          const suggestionResults = await TranslateModule.getSuggestions(inputText, 3);
          setSuggestions(Array.isArray(suggestionResults) ? suggestionResults : []);
        } catch (suggestionError) {
          // Suggestions not available, that's okay
          setSuggestions([]);
        }
      } catch (error) {
        setTranslatedText('Error occurred');
        setSuggestions([]);
      }
    };

    const timer = setTimeout(translateText, 100); // Slightly longer delay for suggestions
    return () => clearTimeout(timer);
  }, [inputText]);

  // Copy text to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await Clipboard.setString(text);
      Alert.alert('Copied!', 'Text copied to clipboard', [{ text: 'OK' }]);
    } catch (error) {
      Alert.alert('Error', 'Failed to copy text', [{ text: 'OK' }]);
    }
  };

  // Paste text from clipboard
  const pasteFromClipboard = async () => {
    try {
      const clipboardText = await Clipboard.getString();
      if (clipboardText) {
        setInputText(clipboardText);
        inputRef.current?.focus();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to paste text', [{ text: 'OK' }]);
    }
  };

  // Clear input
  const clearInput = () => {
    setInputText('');
    setTranslatedText('');
    setSuggestions([]);
    inputRef.current?.focus();
  };
  // Apply suggestion
  const applySuggestion = (suggestion: string) => {
    setTranslatedText(suggestion);
  };

  // Handle external keyboard shortcuts
  const handleKeyPress = (event: any) => {
    const { key, ctrlKey, metaKey } = event.nativeEvent;
    
    // Handle Ctrl+V (Cmd+V on Mac) for paste
    if (key === 'v' && (ctrlKey || metaKey)) {
      event.preventDefault();
      pasteFromClipboard();
      return;
    }
    
    // Handle Ctrl+A (Cmd+A on Mac) for select all
    if (key === 'a' && (ctrlKey || metaKey)) {
      event.preventDefault();
      inputRef.current?.focus();
      // Select all text
      setTimeout(() => {        inputRef.current?.setNativeProps({
          selection: { start: 0, end: inputText.length },
        });
      }, 50);
      return;
    }
    
    // Handle Ctrl+C (Cmd+C on Mac) for copy output
    if (key === 'c' && (ctrlKey || metaKey) && translatedText) {
      event.preventDefault();
      copyToClipboard(translatedText);
      return;
    }
    
    // Handle Escape to clear
    if (key === 'Escape') {
      event.preventDefault();
      clearInput();
      return;
    }
  };

  // Handle focus events for better external keyboard support
  const handleFocus = () => {
    // Ensure proper keyboard handling when input is focused
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
        <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView 
          style={styles.container} 
          keyboardShouldPersistTaps="handled"
          contentInsetAdjustmentBehavior="automatic"
        >
          <Text style={[styles.title, textStyle]}>Tamil Transliterator</Text>
          
          {/* Input Section */}
          <View style={styles.fieldContainer}>
            <View style={styles.labelRow}>
              <Text style={[styles.label, textStyle]}>English Input</Text>
              <View style={styles.buttonRow}>
                <TouchableOpacity 
                  style={[styles.actionButton, { borderColor: isDarkMode ? '#444' : '#ccc' }]}
                  onPress={pasteFromClipboard}
                  accessible={true}
                  accessibilityLabel="Paste from clipboard"
                >
                  <Text style={[styles.buttonText, textStyle]}>📋 Paste</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.actionButton, { borderColor: isDarkMode ? '#444' : '#ccc' }]}
                  onPress={clearInput}
                  accessible={true}
                  accessibilityLabel="Clear input text"
                >
                  <Text style={[styles.buttonText, textStyle]}>🗑️ Clear</Text>
                </TouchableOpacity>
              </View>
            </View>
            <TextInput
              ref={inputRef}
              style={[
                styles.textInput,
                {
                  backgroundColor: isDarkMode ? '#1a1a1a' : '#f8f8f8',
                  color: textStyle.color,
                  borderColor: isDarkMode ? '#333333' : '#dddddd',
                },
              ]}
              placeholder="Type English text here... (vanakkam, nandri, tamil)"
              placeholderTextColor={placeholderColor}
              multiline
              value={inputText}
              onChangeText={setInputText}
              onKeyPress={handleKeyPress}
              onFocus={handleFocus}
              autoCorrect={false}
              spellCheck={false}
              selectTextOnFocus={true}
              enablesReturnKeyAutomatically={true}
              returnKeyType="default"
              blurOnSubmit={false}
              keyboardType="default"
              autoCapitalize="none"
              autoComplete="off"
              accessible={true}
              accessibilityLabel="English text input for Tamil transliteration"
              accessibilityHint="Type English words to convert to Tamil script"
            />
          </View>

          {/* Output Section */}
          <View style={styles.fieldContainer}>
            <View style={styles.labelRow}>
              <Text style={[styles.label, textStyle]}>Tamil Output</Text>
              {translatedText ? (
                <TouchableOpacity 
                  style={[styles.actionButton, { borderColor: isDarkMode ? '#444' : '#ccc' }]}
                  onPress={() => copyToClipboard(translatedText)}
                  accessible={true}
                  accessibilityLabel="Copy Tamil text to clipboard"
                >
                  <Text style={[styles.buttonText, textStyle]}>📄 Copy</Text>
                </TouchableOpacity>
              ) : null}
            </View>
            <TouchableOpacity
              style={[
                styles.textInput,
                styles.outputField,
                {
                  backgroundColor: isDarkMode ? '#1a1a1a' : '#f8f8f8',
                  borderColor: isDarkMode ? '#333333' : '#dddddd',
                },
              ]}
              onPress={() => translatedText && copyToClipboard(translatedText)}
              accessible={true}
              accessibilityLabel="Tamil translation output"
              accessibilityHint="Tap to copy Tamil text"
            >
              <Text 
                style={[styles.outputText, textStyle]}
                selectable={true}
                selectionColor={isDarkMode ? '#4a9eff' : '#007AFF'}
              >
                {translatedText || 'Tamil translation will appear here...'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Suggestions Section */}
          {suggestions.length > 0 && (
            <View style={styles.fieldContainer}>
              <Text style={[styles.label, textStyle]}>Alternative Suggestions</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={styles.suggestionsScrollView}
              >
                <View style={styles.suggestionsContainer}>
                  {suggestions.map((suggestion, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.suggestionChip,                        {
                          backgroundColor: isDarkMode ? '#2a2a2a' : '#e8e8e8',
                          borderColor: isDarkMode ? '#444' : '#ccc',
                        },
                      ]}
                      onPress={() => applySuggestion(suggestion)}
                      accessible={true}
                      accessibilityLabel={`Use suggestion: ${suggestion}`}
                    >
                      <Text style={[styles.suggestionText, textStyle]}>{suggestion}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>
          )}

          {/* Help Text */}
          <View style={styles.helpContainer}>
            <Text style={[styles.helpText, { color: isDarkMode ? '#888' : '#666' }]}>
              💡 External Keyboard Shortcuts:{'\n'}
              • Ctrl+V (Cmd+V): Paste{'\n'}
              • Ctrl+A (Cmd+A): Select All{'\n'}
              • Ctrl+C (Cmd+C): Copy Output{'\n'}
              • Escape: Clear Input{'\n'}
              • Tap output to copy
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  keyboardView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 40,
    letterSpacing: 0.5,
  },
  fieldContainer: {
    marginBottom: 25,
  },
  labelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
    backgroundColor: 'transparent',
    minWidth: 70,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: 'top',
    lineHeight: 22,
  },
  outputField: {
    justifyContent: 'flex-start',
    minHeight: 100,
  },
  outputText: {
    fontSize: 16,
    lineHeight: 22,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  suggestionsScrollView: {
    marginTop: 8,
  },
  suggestionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    gap: 8,
  },
  suggestionChip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    marginHorizontal: 4,
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  helpContainer: {
    marginTop: 20,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(128, 128, 128, 0.1)',
  },
  helpText: {
    fontSize: 13,
    lineHeight: 18,
    fontStyle: 'italic',
  },
});

export default App;
