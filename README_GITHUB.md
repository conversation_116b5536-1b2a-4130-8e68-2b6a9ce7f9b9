# React Native C++ Translation App 🚀

An Android React Native application demonstrating real-time character translation using high-performance C++ native modules with automatic JavaScript fallback.

## ✨ Features

- **Real-time Translation**: Translates text instantly (a→b, b→c, z→a)
- **High Performance**: C++ native modules for optimal speed
- **Fallback System**: Automatic JavaScript fallback if native module fails
- **Android Focused**: Optimized specifically for Android development
- **TypeScript Support**: Full TypeScript integration with proper type definitions

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Native  │    │   TypeScript     │    │   C++ Native    │
│      UI         │◄──►│   Interface      │◄──►│     Module      │
│    (App.tsx)    │    │ (TranslateModule)│    │ (TranslateModule│
└─────────────────┘    └──────────────────┘    │    .cpp/.h)     │
                                                └─────────────────┘
                       ┌──────────────────┐
                       │   JavaScript     │
                       │    Fallback      │
                       │ (Automatic fail- │
                       │   safe backup)   │
                       └──────────────────┘
```

## 🎯 Demo

```
Input:  "hello"
Output: "ifmmp" (each character shifted by +1)

Input:  "world"  
Output: "xpsme"

Input:  "react"
Output: "sfbdu"
```

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+**
- **Android Studio** with NDK and CMake
- **JDK 17**

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/react-native-cpp-translation.git
cd react-native-cpp-translation

# Install dependencies
npm install

# Start Metro bundler
npm start

# Run on Android (in a new terminal)
npx react-native run-android
```

## 📱 Development

### Testing the App

1. Open the app on your Android device/emulator
2. Type any text in the input field
3. See real-time translation in the output field
4. The app automatically uses C++ module or falls back to JavaScript

### Making Changes

**React Native Code:**
- Edit `App.tsx` or files in `src/`
- Press `r` in Metro terminal to reload

**C++ Code:**
- Edit files in `cpp/` folder
- Rebuild: `npx react-native run-android`

## 🏗️ Project Structure

```
TranslateDemo/
├── App.tsx                           # Main React Native UI
├── src/
│   ├── TranslateModule.ts            # TypeScript interface
│   └── TranslateModuleFallback.ts    # JavaScript fallback
├── cpp/
│   ├── TranslateModule.h             # C++ header
│   └── TranslateModule.cpp           # C++ implementation
├── android/
│   └── app/src/main/
│       ├── cpp/translate-lib.cpp     # JNI wrapper
│       └── java/com/translatedemo/   # Java bridge classes
└── docs/
    ├── HOW_IT_WORKS.md              # Detailed architecture
    └── INTEGRATION_GUIDE.md         # Step-by-step integration
```

## 🛠️ Build for Production

```bash
cd android
./gradlew assembleRelease
# Output: android/app/build/outputs/apk/release/
```

## 📚 Documentation

- **[HOW_IT_WORKS.md](HOW_IT_WORKS.md)** - Detailed architecture explanation
- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Step-by-step integration guide
- **[README.md](README.md)** - Complete development setup guide

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React Native community for excellent documentation
- Android NDK team for C++ integration support
- TypeScript team for seamless integration

---

⭐ **Star this repo if you found it helpful!**
