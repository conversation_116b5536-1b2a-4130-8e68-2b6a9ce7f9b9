#include "SMCTransliterator_Complete.h"
#include <algorithm>
#include <cctype>

SMCTransliterator::SMCTransliterator() {
    initializeMappings();
}

void SMCTransliterator::initializeMappings() {
    // Initialize character mappings
    charMappings = {
        // Vowels
        {"a", "அ"}, {"aa", "ஆ"}, {"i", "இ"}, {"ii", "ஈ"},
        {"u", "உ"}, {"uu", "ஊ"}, {"e", "எ"}, {"ee", "ஏ"},
        {"ai", "ஐ"}, {"o", "ஒ"}, {"oo", "ஓ"}, {"au", "ஔ"},
        
        // Consonants
        {"k", "க்"}, {"g", "க்"}, {"ch", "ச்"}, {"j", "ஜ்"},
        {"t", "ட்"}, {"d", "ட்"}, {"n", "ன்"}, {"th", "த்"},
        {"p", "ப்"}, {"b", "ப்"}, {"m", "ம்"}, {"y", "ய்"},
        {"r", "ர்"}, {"l", "ல்"}, {"v", "வ்"}, {"s", "ச்"},
        {"h", "ஹ்"}, {"zh", "ழ்"}, {"z", "ழ்"}, {"ll", "ள்"},
        {"rr", "ற்"}, {"nn", "ண்"}
    };
    
    // Initialize word dictionary
    wordDictionary = {
        {"vanakkam", "வணக்கம்"}, {"nandri", "நன்றி"}, {"tamil", "தமிழ்"},
        {"amma", "அம்மா"}, {"appa", "அப்பா"}, {"naan", "நான்"},
        {"neenga", "நீங்கள்"}, {"enna", "என்ன"}, {"eppo", "எப்போ"},
        {"enga", "எங்க"}, {"computer", "கம்ப்யூட்டர்"}, {"mobile", "மொபைல்"},
        {"internet", "இண்டர்நெட்"}, {"school", "ஸ்கூல்"}, {"college", "கல்லூரி"},
        {"hospital", "மருத்துவமனை"}, {"office", "அலுவலகம்"}, {"house", "வீடு"},
        {"car", "கார்"}, {"bus", "பஸ்"}, {"train", "ரயில்"}, {"book", "புத்தகம்"},
        {"pen", "பேனா"}, {"water", "தண்ணீர்"}, {"food", "உணவு"},
        {"rice", "சாதம்"}, {"money", "பணம்"}, {"time", "நேரம்"},
        {"work", "வேலை"}, {"friend", "நண்பன்"}, {"family", "குடும்பம்"},
        {"love", "காதல்"}, {"happy", "சந்தோஷம்"}, {"good", "நல்ல"},
        {"bad", "கெட்ட"}, {"big", "பெரிய"}, {"small", "சின்ன"},
        {"hello", "வணக்கம்"}, {"thanks", "நன்றி"}, {"welcome", "வரவேற்கிறோம்"},
        {"yes", "ஆம்"}, {"no", "இல்லை"}
    };
}

std::string SMCTransliterator::transliterate(const std::string& input) {
    if (input.empty()) return "";
    
    std::string lowerInput = input;
    std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
    
    // Check for exact word match first
    auto wordIt = wordDictionary.find(lowerInput);
    if (wordIt != wordDictionary.end()) {
        return wordIt->second;
    }
    
    // Character-by-character transliteration
    std::string result;
    for (size_t i = 0; i < lowerInput.length(); ) {
        bool found = false;
        
        // Try longer patterns first
        for (int len = std::min(3, (int)(lowerInput.length() - i)); len >= 1; len--) {
            std::string substr = lowerInput.substr(i, len);
            auto charIt = charMappings.find(substr);
            if (charIt != charMappings.end()) {
                result += charIt->second;
                i += len;
                found = true;
                break;
            }
        }
        
        if (!found) {
            result += lowerInput[i];
            i++;
        }
    }
    
    return result.empty() ? input : result;
}

std::vector<std::string> SMCTransliterator::getSuggestions(const std::string& input, int maxSuggestions) {
    std::vector<std::string> suggestions;
    
    // Add primary translation
    std::string primary = transliterate(input);
    suggestions.push_back(primary);
    
    // Add variations based on similar words
    std::string lowerInput = input;
    std::transform(lowerInput.begin(), lowerInput.end(), lowerInput.begin(), ::tolower);
    
    for (const auto& pair : wordDictionary) {
        if (suggestions.size() >= maxSuggestions) break;
        
        if (pair.first.find(lowerInput) != std::string::npos || 
            lowerInput.find(pair.first) != std::string::npos) {
            if (std::find(suggestions.begin(), suggestions.end(), pair.second) == suggestions.end()) {
                suggestions.push_back(pair.second);
            }
        }
    }
    
    // Resize to max suggestions
    if (suggestions.size() > maxSuggestions) {
        suggestions.resize(maxSuggestions);
    }
    
    return suggestions;
}

bool SMCTransliterator::isInDictionary(const std::string& word) {
    std::string lowerWord = word;
    std::transform(lowerWord.begin(), lowerWord.end(), lowerWord.begin(), ::tolower);
    return wordDictionary.find(lowerWord) != wordDictionary.end();
}
