# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯 Tamil Transliteration Engine
- **Complete Tamil script support** with proper Unicode rendering
- **Contextual transliteration** for accurate word formation
- **Dictionary-based suggestions** for alternative spellings
- **Real-time conversion** as you type

### ⌨️ External Keyboard Support
- **USB OTG keyboards** - Connect any USB keyboard via OTG adapter
- **Bluetooth keyboards** - Wireless keyboard support
- **Comprehensive shortcuts**:
  - `Ctrl+V` (Cmd+V): Paste text
  - `Ctrl+A` (Cmd+A): Select all text
  - `Ctrl+C` (Cmd+C): Copy Tamil output
  - `Escape`: Clear all fields

### 📋 Copy/Paste Functionality
- **One-tap copy** buttons for quick text sharing
- **Clipboard integration** with system-wide copy/paste
- **Selectable text** in output areas
- **Error handling** for clipboard operations

### 🎨 Modern UI/UX
- **Dark/Light mode** automatic switching
- **Responsive design** for various screen sizes
- **Accessibility support** with proper labels and hints
- **Smooth animations** and intuitive interactions
   - Android 14 (API Level 34)
   - Android 13 (API Level 33)
   - Android 12 (API Level 31)
5. In **SDK Tools** tab, install:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android SDK Platform-Tools
   - Android Emulator
   - Intel x86 Emulator Accelerator (HAXM installer)
   - CMake (13.8.1)

#### 3.3 Set Environment Variables

**Windows (PowerShell):**
```powershell
# Add to your PowerShell profile or set permanently in System Properties
$env:ANDROID_HOME = "C:\Users\<USER>\AppData\Local\Android\Sdk"
$env:PATH += ";$env:ANDROID_HOME\platform-tools;$env:ANDROID_HOME\tools;$env:ANDROID_HOME\tools\bin"
```

*
#### 3.4 Create Android Virtual Device (AVD)
1. Open Android Studio
2. Go to **Tools > AVD Manager**
3. Click **Create Virtual Device**
4. Choose **Phone > Pixel 6** (or any modern device)
5. Select **API Level 34** system image
6. Click **Next** and **Finish**

### Step 4: Verify Installation

```bash
# Check Node.js
node --version  # Should be 18+

# Check npm
npm --version

# Check React Native CLI
npx react-native --version

# Check Java
java -version  # Should be 17+

# Verify Android setup
npx react-native doctor
```

## 🚀 Quick Start

### 📱 Download & Install
- **Latest Release**: [TamilTransliterator-v2.0.apk](releases/TamilTransliterator-v2.0.apk) (52.9MB)
- **Previous Version**: [TranslateDemo-v1.0.apk](releases/TranslateDemo-v1.0.apk) (50MB)

### 🎮 How to Use
1. **Install the APK** on your Android device
2. **Open the Tamil Transliterator** app
3. **Type English words** in the input field (try: vanakkam, nandri, tamil)
4. **See instant Tamil translation** in the output area
5. **Connect external keyboard** via USB OTG or Bluetooth for enhanced productivity
6. **Use keyboard shortcuts** for faster workflow
7. **Tap copy button** or use Ctrl+C to copy Tamil text

### 🔌 External Keyboard Setup
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

## 🛠️ Development Setup
````markdown
# Tamil Transliterator - React Native C++ App

A sophisticated React Native application with integrated C++ Tamil transliteration engine. Convert English text to Tamil script in real-time with advanced external keyboard support and copy/paste functionality.

## 📋 What You'll Build

A powerful Tamil transliteration app that:
- **Real-time Tamil transliteration** (vanakkam → வணக்கம், nandri → நன்றி)
- **External keyboard support** (USB OTG & Bluetooth keyboards)
- **Copy/paste functionality** with comprehensive keyboard shortcuts
- **Alternative suggestions** for better transliteration options
- **High-performance C++ engine** (SMCTransliterator) for accurate results
- **Automatic JavaScript fallback** for compatibility
- **Modern UI** with dark mode support
- **Cross-platform** Android support

## ✨ Key Features

### 🎯