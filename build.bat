@echo off
echo ========================================
echo Tamil Transliterator - Multi-Platform Build
echo ========================================
echo.

echo Choose build target:
echo [1] Windows Desktop
echo [2] Android APK
echo [3] Both platforms
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto build_desktop
if "%choice%"=="2" goto build_android
if "%choice%"=="3" goto build_both
echo Invalid choice. Exiting...
pause
exit /b 1

:build_desktop
echo.
echo Building Windows Desktop Application...
echo.

echo [1/4] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [2/4] Building native C++ module...
call npm run build-native
if errorlevel 1 (
    echo ERROR: Failed to build native module
    echo.
    echo Make sure you have:
    echo - Visual Studio Build Tools installed
    echo - Python 3.7+ available
    echo - Windows SDK installed
    pause
    exit /b 1
)

echo [3/4] Building Electron application...
call npm run build-desktop
if errorlevel 1 (
    echo ERROR: Failed to build Electron application
    pause
    exit /b 1
)

echo [4/4] Verifying build output...
if exist "release\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created successfully
) else (
    echo ❌ Installer not found
)

if exist "release\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version created successfully
) else (
    echo ❌ Portable version not found
)

echo.
echo ✅ DESKTOP BUILD COMPLETED!
echo 📦 Files: release\Tamil Transliterator Setup 1.0.0.exe
start explorer "release"
goto end

:build_android
echo.
echo Building Android APK...
echo.

echo [1/3] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [2/3] Building Android APK...
call npm run build-android
if errorlevel 1 (
    echo ERROR: Failed to build Android APK
    echo.
    echo Make sure you have:
    echo - Android Studio installed
    echo - Android SDK configured
    echo - Java 11+ available
    pause
    exit /b 1
)

echo [3/3] Verifying APK output...
if exist "android\app\build\outputs\apk\release\app-release.apk" (
    echo ✓ Android APK created successfully
    if not exist "release" mkdir release
    copy "android\app\build\outputs\apk\release\app-release.apk" "release\TamilTransliterator-v1.0.apk"
) else (
    echo ❌ Android APK not found
)

echo.
echo ✅ ANDROID BUILD COMPLETED!
echo 📦 Files: release\TamilTransliterator-v1.0.apk
start explorer "release"
goto end

:build_both
echo.
echo Building both Desktop and Android...
call :build_desktop
call :build_android
echo.
echo ✅ ALL BUILDS COMPLETED!
echo 📦 Desktop: release\Tamil Transliterator Setup 1.0.0.exe
echo 📦 Android: release\TamilTransliterator-v1.0.apk

:end
echo.
echo 🚀 Ready for distribution!
pause
