@echo off
echo ========================================
echo Tamil Transliterator Desktop - Build
echo ========================================
echo.

echo Building production-ready application...
echo.

echo [1/4] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo [2/4] Building native C++ module...
call npm run build-native
if errorlevel 1 (
    echo ERROR: Failed to build native module
    echo.
    echo Make sure you have:
    echo - Visual Studio Build Tools installed
    echo - Python 3.7+ available
    echo - Windows SDK installed
    pause
    exit /b 1
)

echo [3/4] Building Electron application...
call npm run build
if errorlevel 1 (
    echo ERROR: Failed to build Electron application
    pause
    exit /b 1
)

echo [4/4] Verifying build output...
if exist "release\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created successfully
) else (
    echo ❌ Installer not found
)

if exist "release\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version created successfully
) else (
    echo ❌ Portable version not found
)

echo.
echo ========================================
echo ✅ BUILD COMPLETED!
echo ========================================
echo.
echo 📦 Distribution files:
echo - Installer: release\Tamil Transliterator Setup 1.0.0.exe
echo - Portable: release\win-unpacked\Tamil Transliterator.exe
echo.
echo 🚀 Ready for distribution!
echo.

start explorer "release"
pause
