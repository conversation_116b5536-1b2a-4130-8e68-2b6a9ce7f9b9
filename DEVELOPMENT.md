# Development Guide - Tamil Transliterator

This guide covers development setup, architecture, and contribution guidelines for the Tamil Transliterator project.

## 🏗️ Project Architecture

### Multi-Platform Design
The project is designed to support both Windows Desktop and Android Mobile platforms while sharing core functionality.

```
tamil-transliterator/
├── 📱 Android (React Native)
├── 🖥️ Desktop (Electron)
├── ⚡ Shared C++ Engine
└── 🔧 Build Tools
```

### Technology Stack

#### Frontend Frameworks
- **Windows Desktop**: Electron + React
- **Android Mobile**: React Native
- **Shared UI**: TypeScript + React components

#### Backend Engine
- **Core Engine**: C++ transliteration library
- **Desktop Binding**: N-API native module
- **Android Binding**: JNI native library
- **Fallback**: JavaScript implementation

#### Build Systems
- **Desktop**: Electron Builder + node-gyp
- **Android**: Gradle + CMake + NDK
- **Package Manager**: npm

## 📁 Project Structure

```
tamil-transliterator/
├── android/                    # Android React Native project
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── cpp/           # Android C++ native code
│   │   │   ├── java/          # Java bridge code
│   │   │   └── res/           # Android resources
│   │   └── build.gradle       # Android app configuration
│   ├── gradle/                # Gradle wrapper
│   └── build.gradle          # Android project configuration
├── cpp/                       # Shared C++ transliteration engine
│   ├── SMCTransliterator_Complete.h  # Main transliterator
│   ├── SMCTransliterator_Complete.cpp
│   ├── TranslateModule.h      # Module interface
│   └── TranslateModule.cpp    # Module implementation
├── electron/                  # Electron desktop application
│   ├── main.js               # Main Electron process
│   ├── preload.js            # Preload script
│   ├── renderer/             # Renderer process files
│   └── assets/               # Desktop assets
├── native/                    # Native C++ module for desktop
│   ├── src/                  # N-API binding code
│   ├── binding.gyp           # node-gyp configuration
│   └── package.json          # Native module package
├── src/                       # Shared TypeScript modules
│   ├── TranslateModule.ts     # Main module interface
│   └── TranslateModuleFallback.ts  # JavaScript fallback
├── scripts/                   # Build and utility scripts
│   └── build-native.js       # Native module build script
├── release/                   # Built applications
│   ├── windows/              # Windows builds
│   └── android/              # Android builds
├── App.tsx                    # Main React Native component
├── package.json               # Main project configuration
├── babel.config.js            # Babel configuration
├── metro.config.js            # Metro bundler configuration
├── tsconfig.json              # TypeScript configuration
├── build.bat                  # Windows build script
├── dev.bat                    # Windows development script
└── README.md                  # Project documentation
```

## 🛠️ Development Setup

### Prerequisites

#### Required Software
```bash
# Node.js (18.x or later)
node --version

# npm (comes with Node.js)
npm --version

# Git
git --version
```

#### Windows Desktop Development
```bash
# Visual Studio 2019 or later
# Python 3.8+ (for node-gyp)
python --version

# Windows SDK
# Visual C++ Build Tools
```

#### Android Development
```bash
# Android Studio
# Android SDK (API level 24+)
# Android NDK
# Java 11+
java --version
```

### Environment Setup

#### Clone Repository
```bash
git clone <repository-url>
cd tamil-transliterator
```

#### Install Dependencies
```bash
# Install all dependencies
npm install

# This will automatically build the native module
```

#### Environment Variables
```bash
# For Android development
export ANDROID_HOME=/path/to/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools

# For Windows (if needed)
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
```

## 🚀 Development Commands

### Quick Start
```bash
# Windows Desktop Development
npm run windows

# Android Development (device/emulator required)
npm run android

# Metro Bundler Only
npm start
```

### Build Commands
```bash
# Build Windows installer
npm run build-desktop

# Build Android APK
npm run build-android

# Build native C++ module only
npm run build-native

# Interactive build script
./build.bat
```

### Development Scripts
```bash
# Interactive development script
./dev.bat

# Clean build artifacts
./clean.bat

# Lint code
npm run lint

# Run tests
npm test
```

## 🔧 Development Workflow

### 1. Setting Up Development Environment
```bash
# Clone and setup
git clone <repo>
cd tamil-transliterator
npm install

# Verify setup
npm run windows  # Should open desktop app
npm run android  # Should install on device/emulator
```

### 2. Making Changes

#### Frontend Changes (UI)
- Edit `App.tsx` for React Native components
- Edit `electron/renderer/` for desktop-specific UI
- Changes reflect immediately with hot reload

#### Backend Changes (C++ Engine)
- Edit files in `cpp/` directory
- Rebuild native module: `npm run build-native`
- Restart application to see changes

#### Platform-Specific Changes
- **Android**: Edit files in `android/` directory
- **Desktop**: Edit files in `electron/` directory

### 3. Testing Changes
```bash
# Test on Windows
npm run windows

# Test on Android
npm run android

# Run automated tests
npm test
```

### 4. Building for Distribution
```bash
# Build both platforms
./build.bat

# Or build individually
npm run build-desktop
npm run build-android
```

## 🧪 Testing

### Manual Testing
1. **Functionality Testing**
   - Test transliteration accuracy
   - Test all UI features
   - Test keyboard shortcuts
   - Test clipboard operations

2. **Platform Testing**
   - Test on different Windows versions
   - Test on different Android devices
   - Test performance on low-end devices

3. **Edge Cases**
   - Test with long text
   - Test with special characters
   - Test with empty input
   - Test error scenarios

### Automated Testing
```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
```

## 🐛 Debugging

### Windows Desktop
```bash
# Enable debug mode
npm run electron-dev

# Open DevTools automatically
# Check console for errors
# Use debugger statements
```

### Android
```bash
# Enable debug mode
npm run android

# View logs
adb logcat

# React Native debugger
# Chrome DevTools
```

### Native Module Debugging
```bash
# Verbose build output
npm run build-native -- --verbose

# Check native module loading
# Add console.log statements
# Use native debugging tools
```

## 📦 Building and Distribution

### Windows Desktop
```bash
# Development build
npm run electron-dev

# Production build
npm run build-desktop

# Output: release/windows/
```

### Android
```bash
# Development build
npm run android

# Production build
npm run build-android

# Output: android/app/build/outputs/apk/
```

### Release Process
1. Update version in `package.json`
2. Test on both platforms
3. Build production versions
4. Test built applications
5. Create release documentation
6. Distribute to users

## 🤝 Contributing

### Code Style
- Use TypeScript for all new code
- Follow existing code formatting
- Add comments for complex logic
- Use meaningful variable names

### Commit Guidelines
```bash
# Format: type(scope): description
feat(android): add new transliteration feature
fix(desktop): resolve clipboard issue
docs(readme): update installation instructions
```

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Update documentation
5. Submit pull request
6. Address review feedback

## 📚 Additional Resources

### Documentation
- [React Native Documentation](https://reactnative.dev/)
- [Electron Documentation](https://electronjs.org/)
- [Android NDK Guide](https://developer.android.com/ndk)
- [Node.js N-API Documentation](https://nodejs.org/api/n-api.html)

### Tools
- [Android Studio](https://developer.android.com/studio)
- [Visual Studio Code](https://code.visualstudio.com/)
- [React Native Debugger](https://github.com/jhen0409/react-native-debugger)

---

**Happy Coding! 🚀**
