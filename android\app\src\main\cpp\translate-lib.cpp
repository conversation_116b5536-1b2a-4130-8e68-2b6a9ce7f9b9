#include <jni.h>
#include <string>
#include <vector>
#include "../cpp/TranslateModule.h"

extern "C" JNIEXPORT jstring JNICALL
Java_com_translatedemo_TranslateModule_translateText(
    JNIEnv *env,
    jobject /* this */,
    jstring input) {
    
    const char *inputStr = env->GetStringUTFChars(input, 0);
    std::string result = TranslateModule::translateText(std::string(inputStr));
    env->ReleaseStringUTFChars(input, inputStr);
    
    return env->NewStringUTF(result.c_str());
}

extern "C" JNIEXPORT jobjectArray JNICALL
Java_com_translatedemo_TranslateModule_getSuggestions(
    JNIEnv *env,
    jobject /* this */,
    jstring input,
    jint maxSuggestions) {
    
    const char *inputStr = env->GetStringUTFChars(input, 0);
    std::vector<std::string> suggestions = TranslateModule::getSuggestions(std::string(inputStr), maxSuggestions);
    env->ReleaseStringUTFChars(input, inputStr);
    
    // Convert std::vector<std::string> to jobjectArray
    jobjectArray result = env->NewObjectArray(suggestions.size(), env->FindClass("java/lang/String"), nullptr);
    for (size_t i = 0; i < suggestions.size(); ++i) {
        jstring suggestion = env->NewStringUTF(suggestions[i].c_str());
        env->SetObjectArrayElement(result, i, suggestion);
        env->DeleteLocalRef(suggestion);
    }
    
    return result;
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_translatedemo_TranslateModule_transliterateWithContext(
    JNIEnv *env,
    jobject /* this */,
    jstring input,
    jstring context) {
    
    const char *inputStr = env->GetStringUTFChars(input, 0);
    const char *contextStr = env->GetStringUTFChars(context, 0);
    
    std::string result = TranslateModule::transliterateWithContext(
        std::string(inputStr), 
        std::string(contextStr)
    );
    
    env->ReleaseStringUTFChars(input, inputStr);
    env->ReleaseStringUTFChars(context, contextStr);
    
    return env->NewStringUTF(result.c_str());
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_translatedemo_TranslateModule_isWordInDictionary(
    JNIEnv *env,
    jobject /* this */,
    jstring word) {
    
    const char *wordStr = env->GetStringUTFChars(word, 0);
    bool result = TranslateModule::isWordInDictionary(std::string(wordStr));
    env->ReleaseStringUTFChars(word, wordStr);
    
    return result ? JNI_TRUE : JNI_FALSE;
}
