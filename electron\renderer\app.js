// Tamil Transliterator Desktop App - Renderer Process
class TamilTransliteratorApp {
    constructor() {
        this.elements = {};
        this.state = {
            isDarkMode: false,
            isHelpOpen: false,
            lastTranslationTime: 0,
            translationTimeout: null
        };
        
        this.init();
    }

    init() {
        this.cacheElements();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.setupMenuHandlers();
        this.initializeTheme();
        this.hideLoadingOverlay();
        this.showToast('Tamil Transliterator Ready!', 'success');
    }

    cacheElements() {
        this.elements = {
            inputText: document.getElementById('input-text'),
            outputText: document.getElementById('output-text'),
            suggestionsSection: document.getElementById('suggestions-section'),
            suggestionsContainer: document.getElementById('suggestions-container'),
            charCount: document.getElementById('char-count'),
            wordCount: document.getElementById('word-count'),
            engineStatus: document.getElementById('engine-status'),
            translationTime: document.getElementById('translation-time'),
            themeToggle: document.getElementById('theme-toggle'),
            settingsBtn: document.getElementById('settings-btn'),
            pasteBtn: document.getElementById('paste-btn'),
            clearBtn: document.getElementById('clear-btn'),
            copyBtn: document.getElementById('copy-btn'),
            saveBtn: document.getElementById('save-btn'),
            helpPanel: document.getElementById('help-panel'),
            loadingOverlay: document.getElementById('loading-overlay'),
            toastContainer: document.getElementById('toast-container')
        };
    }

    setupEventListeners() {
        // Input text changes
        this.elements.inputText.addEventListener('input', (e) => {
            this.handleInputChange(e.target.value);
        });

        // Button clicks
        this.elements.pasteBtn.addEventListener('click', () => this.pasteText());
        this.elements.clearBtn.addEventListener('click', () => this.clearInput());
        this.elements.copyBtn.addEventListener('click', () => this.copyOutput());
        this.elements.saveBtn.addEventListener('click', () => this.saveOutput());
        this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
        this.elements.settingsBtn.addEventListener('click', () => this.toggleHelp());

        // Focus management
        this.elements.inputText.addEventListener('focus', () => {
            this.elements.inputText.select();
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            const isCtrlOrCmd = e.ctrlKey || e.metaKey;
            
            if (isCtrlOrCmd) {
                switch (e.key.toLowerCase()) {
                    case 'v':
                        if (document.activeElement === this.elements.inputText) {
                            // Let default paste behavior work
                            return;
                        }
                        e.preventDefault();
                        this.pasteText();
                        break;
                    case 'a':
                        if (document.activeElement === this.elements.inputText) {
                            // Let default select all work
                            return;
                        }
                        e.preventDefault();
                        this.elements.inputText.focus();
                        this.elements.inputText.select();
                        break;
                    case 'c':
                        if (document.activeElement === this.elements.inputText) {
                            // Let default copy work
                            return;
                        }
                        e.preventDefault();
                        this.copyOutput();
                        break;
                    case 's':
                        e.preventDefault();
                        this.saveOutput();
                        break;
                    case 'n':
                        e.preventDefault();
                        this.clearInput();
                        break;
                }
            } else {
                switch (e.key) {
                    case 'Escape':
                        e.preventDefault();
                        if (this.state.isHelpOpen) {
                            this.toggleHelp();
                        } else {
                            this.clearInput();
                        }
                        break;
                    case 'F1':
                        e.preventDefault();
                        this.toggleHelp();
                        break;
                }
            }
        });
    }

    setupMenuHandlers() {
        if (window.electronAPI) {
            window.electronAPI.onMenuNew(() => this.clearInput());
            window.electronAPI.onMenuOpen((event, filePath) => this.openFile(filePath));
            window.electronAPI.onMenuSave(() => this.saveOutput());
        }
    }

    initializeTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
    }

    setTheme(theme) {
        this.state.isDarkMode = theme === 'dark';
        document.documentElement.setAttribute('data-theme', theme);
        
        const themeIcon = this.elements.themeToggle.querySelector('.theme-icon');
        themeIcon.textContent = this.state.isDarkMode ? '☀️' : '🌙';
        
        localStorage.setItem('theme', theme);
    }

    toggleTheme() {
        const newTheme = this.state.isDarkMode ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    toggleHelp() {
        this.state.isHelpOpen = !this.state.isHelpOpen;
        this.elements.helpPanel.classList.toggle('open', this.state.isHelpOpen);
    }

    hideLoadingOverlay() {
        setTimeout(() => {
            this.elements.loadingOverlay.classList.remove('show');
        }, 1000);
    }

    handleInputChange(text) {
        this.updateCounts(text);
        
        // Clear previous timeout
        if (this.state.translationTimeout) {
            clearTimeout(this.state.translationTimeout);
        }
        
        // Debounce translation
        this.state.translationTimeout = setTimeout(() => {
            this.translateText(text);
        }, 150);
    }

    updateCounts(text) {
        const charCount = text.length;
        const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
        
        this.elements.charCount.textContent = `${charCount} characters`;
        this.elements.wordCount.textContent = `${wordCount} words`;
    }

    async translateText(text) {
        if (!text.trim()) {
            this.elements.outputText.textContent = '';
            this.elements.suggestionsSection.style.display = 'none';
            this.elements.copyBtn.disabled = true;
            this.elements.saveBtn.disabled = true;
            return;
        }

        const startTime = performance.now();
        
        try {
            // Show loading state
            this.elements.engineStatus.innerHTML = `
                <span class="status-indicator" style="background-color: var(--warning-color);"></span>
                Translating...
            `;

            // Translate text
            const translatedText = await window.electronAPI.translateText(text);
            
            // Get suggestions
            const suggestions = await window.electronAPI.getSuggestions(text, 5);
            
            const endTime = performance.now();
            const translationTime = Math.round(endTime - startTime);
            
            // Update UI
            this.elements.outputText.textContent = translatedText;
            this.displaySuggestions(suggestions, translatedText);
            
            // Update status
            this.elements.engineStatus.innerHTML = `
                <span class="status-indicator" style="background-color: var(--success-color);"></span>
                C++ Engine Ready
            `;
            this.elements.translationTime.textContent = `${translationTime}ms`;

            // Log for debugging
            console.log(`Translation: "${text}" → "${translatedText}" (${translationTime}ms)`);
            
            // Enable buttons
            this.elements.copyBtn.disabled = false;
            this.elements.saveBtn.disabled = false;
            
        } catch (error) {
            console.error('Translation error:', error);
            this.elements.outputText.textContent = 'Translation error occurred';
            this.elements.engineStatus.innerHTML = `
                <span class="status-indicator" style="background-color: var(--error-color);"></span>
                Engine Error
            `;
            this.showToast('Translation failed', 'error');
        }
    }

    displaySuggestions(suggestions, currentTranslation) {
        // Filter out the current translation from suggestions
        const filteredSuggestions = suggestions.filter(s => s !== currentTranslation);
        
        if (filteredSuggestions.length === 0) {
            this.elements.suggestionsSection.style.display = 'none';
            return;
        }

        this.elements.suggestionsContainer.innerHTML = '';
        
        filteredSuggestions.forEach(suggestion => {
            const chip = document.createElement('div');
            chip.className = 'suggestion-chip';
            chip.textContent = suggestion;
            chip.addEventListener('click', () => {
                this.elements.outputText.textContent = suggestion;
                this.showToast('Suggestion applied', 'success');
            });
            this.elements.suggestionsContainer.appendChild(chip);
        });
        
        this.elements.suggestionsSection.style.display = 'block';
    }

    async pasteText() {
        try {
            const text = await window.clipboardAPI.readText();
            this.elements.inputText.value = text;
            this.elements.inputText.focus();
            this.handleInputChange(text);
            this.showToast('Text pasted', 'success');
        } catch (error) {
            console.error('Paste error:', error);
            this.showToast('Failed to paste text', 'error');
        }
    }

    clearInput() {
        this.elements.inputText.value = '';
        this.elements.outputText.textContent = '';
        this.elements.suggestionsSection.style.display = 'none';
        this.elements.copyBtn.disabled = true;
        this.elements.saveBtn.disabled = true;
        this.updateCounts('');
        this.elements.inputText.focus();
        this.showToast('Input cleared', 'success');
    }

    async copyOutput() {
        const text = this.elements.outputText.textContent;
        if (!text || text === 'Tamil translation will appear here...') {
            this.showToast('No text to copy', 'warning');
            return;
        }

        try {
            await window.clipboardAPI.writeText(text);
            this.showToast('Tamil text copied!', 'success');
        } catch (error) {
            console.error('Copy error:', error);
            this.showToast('Failed to copy text', 'error');
        }
    }

    saveOutput() {
        const text = this.elements.outputText.textContent;
        if (!text || text === 'Tamil translation will appear here...') {
            this.showToast('No text to save', 'warning');
            return;
        }

        // Create download link
        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `tamil-translation-${new Date().toISOString().slice(0, 10)}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('File saved!', 'success');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        this.elements.toastContainer.appendChild(toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TamilTransliteratorApp();
});
