@echo off
echo ========================================
echo Tamil Transliterator - Retry Build
echo ========================================
echo.

echo Fixed icon issue - retrying build...
echo.

echo [1/3] Cleaning previous build artifacts...
if exist dist rmdir /s /q dist

echo [2/3] Building Electron application (without custom icon)...
echo This may take several minutes...
call npm run build-electron-win
if errorlevel 1 (
    echo ERROR: Build still failed
    echo.
    echo Possible remaining issues:
    echo 1. Antivirus blocking the build process
    echo 2. Insufficient disk space
    echo 3. Network issues downloading Electron
    echo.
    echo Try:
    echo - Temporarily disable antivirus
    echo - Free up disk space (need ~500MB)
    echo - Check internet connection
    pause
    exit /b 1
)

echo [3/3] Verifying build output...
if exist "dist\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created successfully!
    for %%A in ("dist\Tamil Transliterator Setup 1.0.0.exe") do echo   Size: %%~zA bytes
)

if exist "dist\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version created successfully!
    echo   Location: dist\win-unpacked\Tamil Transliterator.exe
)

echo.
echo ========================================
echo ✅ BUILD SUCCESSFUL!
echo ========================================
echo.
echo 📦 Your distributable files are ready:
echo.
echo 1. INSTALLER: dist\Tamil Transliterator Setup 1.0.0.exe
echo    - Professional Windows installer
echo    - ~100-200MB
echo    - Creates shortcuts and uninstaller
echo.
echo 2. PORTABLE: dist\win-unpacked\Tamil Transliterator.exe
echo    - No installation required
echo    - Copy folder and run
echo    - Perfect for USB distribution
echo.
echo 🚀 Ready to distribute!
echo - Test on another Windows machine first
echo - Users may see Windows Defender warnings (normal)
echo - Tell users to click "More info" → "Run anyway"
echo.

echo Opening dist folder...
start explorer "dist"

pause
