@echo off
echo ========================================
echo Tamil Transliterator - Build Fix
echo ========================================
echo.

echo Fixing build issues...
echo.

echo [1/5] Cleaning all build artifacts...
cd native
if exist build rmdir /s /q build
if exist node_modules rmdir /s /q node_modules
cd ..

echo [2/5] Installing native dependencies...
cd native
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install native dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo [3/5] Setting up build environment...
cd native
echo Configuring node-gyp...
call npx node-gyp clean
call npx node-gyp configure --msvs_version=2022
if errorlevel 1 (
    echo ERROR: Failed to configure build
    cd ..
    pause
    exit /b 1
)

echo [4/5] Building native module...
call npx node-gyp build
if errorlevel 1 (
    echo ERROR: Build failed
    echo.
    echo Troubleshooting:
    echo 1. Ensure Visual Studio 2022 Build Tools are installed
    echo 2. Make sure Windows SDK is available
    echo 3. Try running as Administrator
    cd ..
    pause
    exit /b 1
)
cd ..

echo [5/5] Verifying build...
if exist "native\build\Release\transliterate_addon.node" (
    echo ✓ Native module built successfully!
    echo.
    echo Testing the module...
    node test-native.js
) else (
    echo ❌ Build output not found
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ BUILD FIXED SUCCESSFULLY!
echo ========================================
echo.
echo You can now run:
echo - npm run electron-dev (for development)
echo - npm run dist (for production build)
echo.
pause
