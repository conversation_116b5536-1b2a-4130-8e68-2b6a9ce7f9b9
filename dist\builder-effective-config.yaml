directories:
  output: dist
  buildResources: build
appId: com.tamiltools.transliterator
productName: Tamil Transliterator
files:
  - filter:
      - electron/**/*
      - native/build/Release/*.node
      - node_modules/**/*
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: electron/assets/icon.ico
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
extraResources:
  - from: native/build/Release/
    to: native/
    filter:
      - '*.node'
electronVersion: 28.3.3
