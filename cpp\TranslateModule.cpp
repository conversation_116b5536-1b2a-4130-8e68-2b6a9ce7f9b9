#include "TranslateModule.h"
#include <algorithm>
#include <memory>

// Static instance of the transliterator for efficient reuse
SMCTransliterator& TranslateModule::getTransliterator() {
    static SMCTransliterator instance;
    return instance;
}

std::string TranslateModule::translateText(const std::string& input) {
    if (input.empty()) {
        return "";
    }
    
    try {
        // Use the comprehensive SMC Tamil transliterator
        return getTransliterator().transliterate(input);
    } catch (const std::exception& e) {
        // Fallback to returning input if translation fails
        return input;
    }
}

std::vector<std::string> TranslateModule::getSuggestions(const std::string& input, int maxSuggestions) {
    if (input.empty()) {
        return {};
    }
    
    try {
        return getTransliterator().getSuggestions(input, maxSuggestions);
    } catch (const std::exception& e) {
        // Return basic transliteration as fallback
        return {translateText(input)};
    }
}

std::string TranslateModule::transliterateWithContext(const std::string& input, const std::string& context) {
    if (input.empty()) {
        return "";
    }
    
    try {
        return getTransliterator().transliterateWithContext(input, context);
    } catch (const std::exception& e) {
        // Fallback to basic transliteration
        return translateText(input);
    }
}

bool TranslateModule::isWordInDictionary(const std::string& word) {
    try {
        return getTransliterator().isInDictionary(word);
    } catch (const std::exception& e) {
        return false;
    }
}
