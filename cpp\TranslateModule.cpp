#include "TranslateModule.h"
#include "SimpleTransliterator.h"
#include <algorithm>
#include <memory>
#include <iostream>

// Static instances for efficient reuse
SMCTransliterator& TranslateModule::getTransliterator() {
    static SMCTransliterator instance;
    return instance;
}

SimpleTransliterator& TranslateModule::getSimpleTransliterator() {
    static SimpleTransliterator instance;
    return instance;
}

std::string TranslateModule::translateText(const std::string& input) {
    if (input.empty()) {
        return "";
    }

    try {
        // Try the comprehensive SMC Tamil transliterator first
        std::string result = getTransliterator().transliterate(input);

        // If result is same as input, the SMC engine might not be working
        if (result == input) {
            std::cerr << "SMC engine returned unchanged input, trying simple transliterator..." << std::endl;
            return getSimpleTransliterator().transliterate(input);
        }

        return result;
    } catch (const std::exception& e) {
        std::cerr << "SMC engine failed: " << e.what() << ", using simple transliterator..." << std::endl;
        // Fallback to simple transliterator
        try {
            return getSimpleTransliterator().transliterate(input);
        } catch (const std::exception& e2) {
            std::cerr << "Simple transliterator also failed: " << e2.what() << std::endl;
            return input; // Last resort
        }
    }
}

std::vector<std::string> TranslateModule::getSuggestions(const std::string& input, int maxSuggestions) {
    if (input.empty()) {
        return {};
    }

    try {
        auto suggestions = getTransliterator().getSuggestions(input, maxSuggestions);
        if (!suggestions.empty()) {
            return suggestions;
        }
    } catch (const std::exception& e) {
        std::cerr << "SMC suggestions failed: " << e.what() << std::endl;
    }

    // Fallback to simple transliterator
    try {
        return getSimpleTransliterator().getSuggestions(input, maxSuggestions);
    } catch (const std::exception& e) {
        std::cerr << "Simple suggestions failed: " << e.what() << std::endl;
        // Return basic transliteration as last resort
        return {translateText(input)};
    }
}

std::string TranslateModule::transliterateWithContext(const std::string& input, const std::string& context) {
    if (input.empty()) {
        return "";
    }

    try {
        std::string result = getTransliterator().transliterateWithContext(input, context);
        if (result != input) {
            return result;
        }
    } catch (const std::exception& e) {
        std::cerr << "SMC context translation failed: " << e.what() << std::endl;
    }

    // Fallback to simple transliterator
    try {
        return getSimpleTransliterator().transliterateWithContext(input, context);
    } catch (const std::exception& e) {
        std::cerr << "Simple context translation failed: " << e.what() << std::endl;
        return translateText(input);
    }
}

bool TranslateModule::isWordInDictionary(const std::string& word) {
    try {
        bool result = getTransliterator().isInDictionary(word);
        if (result) {
            return true;
        }
    } catch (const std::exception& e) {
        std::cerr << "SMC dictionary check failed: " << e.what() << std::endl;
    }

    // Fallback to simple transliterator
    try {
        return getSimpleTransliterator().isInDictionary(word);
    } catch (const std::exception& e) {
        std::cerr << "Simple dictionary check failed: " << e.what() << std::endl;
        return false;
    }
}
