@echo off
echo ========================================
echo Tamil Transliterator - Backend Rebuild
echo ========================================
echo.

echo This script will rebuild the C++ backend with fallback support
echo.

echo [1/6] Cleaning previous build...
cd native
if exist build rmdir /s /q build
if exist node_modules rmdir /s /q node_modules
cd ..

echo [2/6] Installing native dependencies...
cd native
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install native dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo [3/6] Configuring build...
cd native
call npx node-gyp clean
call npx node-gyp configure --msvs_version=2022
if errorlevel 1 (
    echo ERROR: Failed to configure build
    cd ..
    pause
    exit /b 1
)

echo [4/6] Building native module with fallback support...
call npx node-gyp build
if errorlevel 1 (
    echo ERROR: Build failed
    echo.
    echo The build failed. This might be due to:
    echo 1. Missing Visual Studio Build Tools
    echo 2. Complex SMC engine compilation issues
    echo 3. Missing Windows SDK
    echo.
    echo The app includes a fallback system that should still work.
    cd ..
    pause
    exit /b 1
)
cd ..

echo [5/6] Testing the rebuilt backend...
echo.
node test-native.js
if errorlevel 1 (
    echo WARNING: Native module test failed, but app might still work with fallback
) else (
    echo ✓ Backend test successful!
)

echo.
echo [6/6] Testing Electron app...
echo Starting Electron app for 10 seconds to verify backend...
timeout /t 3 /nobreak > nul
start /min npm run electron-dev
echo.
echo App started! Check if translation is working.
echo - Type 'vanakkam' and you should see 'வணக்கம்'
echo - Type 'nandri' and you should see 'நன்றி'
echo.
echo If translation works: ✓ Backend is working!
echo If you see English text unchanged: Backend needs more debugging
echo.

echo ========================================
echo Backend rebuild completed!
echo ========================================
echo.
echo What was fixed:
echo - Added SimpleTransliterator fallback system
echo - Enhanced error handling and logging
echo - Improved build configuration
echo.
echo If translation still doesn't work:
echo 1. Check the Electron console for error messages
echo 2. The fallback system should handle basic words
echo 3. Complex SMC engine might need additional debugging
echo.
pause
