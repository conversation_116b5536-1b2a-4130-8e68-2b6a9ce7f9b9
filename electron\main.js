const { app, BrowserWindow, ipc<PERSON>ain, Menu, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js')
        },
        show: false, // Don't show until ready
        titleBarStyle: 'default'
    });

    // Load the app
    mainWindow.loadFile(path.join(__dirname, 'renderer', 'index.html'));

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Focus on the window
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Set up menu
    createMenu();
}

function createMenu() {
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'New',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        mainWindow.webContents.send('menu-new');
                    }
                },
                {
                    label: 'Open',
                    accelerator: 'CmdOrCtrl+O',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'Text Files', extensions: ['txt'] },
                                { name: 'All Files', extensions: ['*'] }
                            ]
                        });
                        
                        if (!result.canceled) {
                            mainWindow.webContents.send('menu-open', result.filePaths[0]);
                        }
                    }
                },
                {
                    label: 'Save',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.send('menu-save');
                    }
                },
                { type: 'separator' },
                {
                    label: 'Exit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' },
                { role: 'selectall' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Help',
            submenu: [
                {
                    label: 'About Tamil Transliterator',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'About Tamil Transliterator',
                            message: 'Tamil Transliterator Desktop',
                            detail: 'Version 1.0.0\nA powerful Tamil transliteration tool with C++ engine.\n\nDeveloped with Electron and Node.js native modules.'
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC handlers for native module communication
ipcMain.handle('translate-text', async (event, text) => {
    try {
        const nativeModule = require('../native/build/Release/transliterate_addon.node');
        return nativeModule.translateText(text);
    } catch (error) {
        console.error('Translation error:', error);
        throw error;
    }
});

ipcMain.handle('get-suggestions', async (event, text, maxSuggestions = 5) => {
    try {
        const nativeModule = require('../native/build/Release/transliterate_addon.node');
        return nativeModule.getSuggestions(text, maxSuggestions);
    } catch (error) {
        console.error('Suggestions error:', error);
        throw error;
    }
});

ipcMain.handle('transliterate-with-context', async (event, text, context = '') => {
    try {
        const nativeModule = require('../native/build/Release/transliterate_addon.node');
        return nativeModule.transliterateWithContext(text, context);
    } catch (error) {
        console.error('Context translation error:', error);
        throw error;
    }
});

ipcMain.handle('is-word-in-dictionary', async (event, word) => {
    try {
        const nativeModule = require('../native/build/Release/transliterate_addon.node');
        return nativeModule.isWordInDictionary(word);
    } catch (error) {
        console.error('Dictionary check error:', error);
        throw error;
    }
});

// File operations
ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const fs = require('fs').promises;
        const content = await fs.readFile(filePath, 'utf8');
        return content;
    } catch (error) {
        console.error('File read error:', error);
        throw error;
    }
});

ipcMain.handle('write-file', async (event, filePath, content) => {
    try {
        const fs = require('fs').promises;
        await fs.writeFile(filePath, content, 'utf8');
        return true;
    } catch (error) {
        console.error('File write error:', error);
        throw error;
    }
});
