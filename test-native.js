// Test script for the native module
const path = require('path');

console.log('Testing Tamil Transliterator Native Module...\n');

try {
    // Try to load the native module
    const nativeModulePath = path.join(__dirname, 'native', 'build', 'Release', 'transliterate_addon.node');
    console.log('Loading native module from:', nativeModulePath);
    
    const transliterator = require(nativeModulePath);
    console.log('✓ Native module loaded successfully!\n');
    
    // Test basic translation
    console.log('Testing basic translation:');
    const testWords = ['vanakkam', 'nandri', 'tamil', 'hello', 'computer', 'amma', 'appa'];
    
    for (const word of testWords) {
        try {
            const result = transliterator.translateText(word);
            console.log(`  ${word} → ${result}`);
        } catch (error) {
            console.log(`  ${word} → ERROR: ${error.message}`);
        }
    }
    
    console.log('\nTesting suggestions:');
    try {
        const suggestions = transliterator.getSuggestions('vanakkam', 3);
        console.log(`  Suggestions for 'vanakkam':`, suggestions);
    } catch (error) {
        console.log(`  Suggestions ERROR: ${error.message}`);
    }
    
    console.log('\nTesting context translation:');
    try {
        const contextResult = transliterator.transliterateWithContext('nandri', 'vanakkam');
        console.log(`  'nandri' with context 'vanakkam' → ${contextResult}`);
    } catch (error) {
        console.log(`  Context translation ERROR: ${error.message}`);
    }
    
    console.log('\nTesting dictionary check:');
    try {
        const isInDict = transliterator.isWordInDictionary('vanakkam');
        console.log(`  'vanakkam' in dictionary: ${isInDict}`);
    } catch (error) {
        console.log(`  Dictionary check ERROR: ${error.message}`);
    }
    
    console.log('\n✅ All tests completed!');
    
} catch (error) {
    console.error('❌ Failed to load native module:');
    console.error('Error:', error.message);
    console.error('\nTroubleshooting:');
    console.error('1. Run: npm run build-native');
    console.error('2. Check that Visual Studio Build Tools are installed');
    console.error('3. Ensure Python is available for node-gyp');
    console.error('4. Try running as Administrator');
    process.exit(1);
}
