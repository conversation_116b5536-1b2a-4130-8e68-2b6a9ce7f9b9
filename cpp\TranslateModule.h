#ifndef TRANSLATEMODULE_H
#define TRANSLATEMODULE_H

#include <string>
#include "SMCTransliterator_Complete.h"

class TranslateModule {
public:
    static std::string translateText(const std::string& input);
    static std::vector<std::string> getSuggestions(const std::string& input, int maxSuggestions = 5);
    static std::string transliterateWithContext(const std::string& input, const std::string& context = "");
    static bool isWordInDictionary(const std::string& word);
private:
    static SMCTransliterator& getTransliterator();
};

#endif // TRANSLATEMODULE_H
