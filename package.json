{"name": "TranslateDemo", "version": "1.0.0", "description": "Tamil transliteration app - Android & Desktop", "author": "Tamil Tools Developer", "main": "electron/main.js", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "windows": "npm run build-native && cross-env NODE_ENV=development electron .", "lint": "eslint .", "start": "react-native start", "test": "jest", "electron": "electron .", "electron-dev": "cross-env NODE_ENV=development electron .", "build-native": "node scripts/build-native.js", "build-desktop": "npm run build-native && electron-builder --win --config.directories.output=release", "build-android": "cd android && ./gradlew assembleRelease", "dist": "npm run build-desktop", "postinstall": "npm run build-native"}, "dependencies": {"@react-native-clipboard/clipboard": "^1.16.2", "react": "19.0.0", "react-native": "0.79.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.19.0", "jest": "^29.6.3", "node-gyp": "^10.0.1", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "build": {"appId": "com.tamiltools.transliterator", "productName": "Tamil Transliterator", "directories": {"output": "release"}, "files": ["electron/**/*", "native/build/Release/*.node", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "extraResources": [{"from": "native/build/Release/", "to": "native/", "filter": ["*.node"]}]}}