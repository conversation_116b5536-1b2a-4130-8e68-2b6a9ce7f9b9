{"name": "tamil-transliterator-desktop", "version": "1.0.0", "description": "Tamil Transliterator Desktop Application with C++ Engine", "author": "Tamil Tools Developer", "main": "electron/main.js", "private": true, "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build-native": "node scripts/build-native.js", "build": "npm run build-native && electron-builder --win --config.directories.output=release", "dist": "npm run build", "postinstall": "npm run build-native"}, "dependencies": {}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1", "node-gyp": "^10.0.1"}, "engines": {"node": ">=18"}, "build": {"appId": "com.tamiltools.transliterator", "productName": "Tamil Transliterator", "directories": {"output": "release"}, "files": ["electron/**/*", "native/build/Release/*.node", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "extraResources": [{"from": "native/build/Release/", "to": "native/", "filter": ["*.node"]}]}}