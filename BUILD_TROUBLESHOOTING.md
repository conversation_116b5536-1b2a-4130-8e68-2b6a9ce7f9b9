# Build Troubleshooting Guide

## The Error You Encountered

```
error C2653: '__napi_TransliterateAddon': is not a class or namespace name
```

This error was caused by an incorrect Node.js addon registration macro. **This has been fixed!**

## Quick Fix

Run the fixed build script:
```bash
./fix-build.bat
```

This script will:
1. Clean all build artifacts
2. Reinstall dependencies
3. Configure the build properly
4. Build the native module
5. Test the module

## Manual Fix Steps

If the automated script doesn't work, follow these steps:

### 1. Clean Everything
```bash
cd native
rmdir /s /q build
rmdir /s /q node_modules
cd ..
```

### 2. Install Dependencies
```bash
cd native
npm install
cd ..
```

### 3. Build with Specific Configuration
```bash
cd native
npx node-gyp clean
npx node-gyp configure --msvs_version=2022
npx node-gyp build
cd ..
```

### 4. Test the Build
```bash
node test-native.js
```

## Common Build Issues & Solutions

### Issue 1: Visual Studio Not Found
**Error**: `MSBuild.exe not found`

**Solution**:
1. Install Visual Studio Community 2022
2. Select "Desktop development with C++" workload
3. Ensure Windows 10/11 SDK is included

### Issue 2: Python Not Found
**Error**: `Python executable not found`

**Solution**:
```bash
npm install -g windows-build-tools
# OR
npm config set python python3.exe
```

### Issue 3: Node-gyp Version Issues
**Error**: Various node-gyp related errors

**Solution**:
```bash
npm install -g node-gyp@latest
npm cache clean --force
```

### Issue 4: Permission Errors
**Error**: Access denied or permission errors

**Solution**:
- Run Command Prompt as Administrator
- Disable antivirus temporarily during build
- Check Windows Defender exclusions

### Issue 5: C++ Standard Version Conflicts
**Error**: `overriding '/std:c++20' with '/std:c++17'`

**Solution**: This is just a warning and can be ignored. The build should still succeed.

## Verification Steps

After a successful build, you should see:
1. File exists: `native/build/Release/transliterate_addon.node`
2. Test script runs: `node test-native.js` shows translations
3. Electron app starts: `npm run electron-dev`

## Alternative: Use Pre-built Binaries

If building continues to fail, you can:
1. Build on a different machine with proper tools
2. Copy the `.node` file to `native/build/Release/`
3. Continue with Electron development

## Getting Help

If you're still having issues:
1. Check the exact error message
2. Verify all prerequisites are installed
3. Try building a simple "hello world" node-gyp module first
4. Consider using Docker for a clean build environment

## Build Environment Requirements

**Minimum Requirements**:
- Windows 10/11
- Node.js 18+
- Python 3.7+
- Visual Studio Build Tools 2019/2022

**Recommended Setup**:
- Visual Studio Community 2022
- Windows 11 SDK (latest)
- Git for Windows
- PowerShell 7+

## Success Indicators

When everything works correctly:
```
✓ Native module built successfully!
✓ Testing the module...
vanakkam → வணக்கம்
nandri → நன்றி
tamil → தமிழ்
✅ All tests completed!
```
