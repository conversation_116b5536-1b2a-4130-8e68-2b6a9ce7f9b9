@echo off
echo ========================================
echo Tamil Transliterator - Build to Release
echo ========================================
echo.

echo Building to 'release' folder to avoid file locks...
echo.

echo [1/4] Creating release directory...
if not exist release mkdir release

echo [2/4] Cleaning release directory...
if exist release rmdir /s /q release 2>nul
mkdir release

echo [3/4] Building Electron app to release folder...
echo This avoids the locked 'dist' folder completely.
echo.

call npx electron-builder --win --config.directories.output=release
if errorlevel 1 (
    echo.
    echo ❌ BUILD FAILED
    echo.
    echo If this also fails, try:
    echo 1. Restart your computer
    echo 2. Run as Administrator
    echo 3. Temporarily disable antivirus
    echo.
    pause
    exit /b 1
)

echo [4/4] Verifying build output...

if exist "release\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created: release\Tamil Transliterator Setup 1.0.0.exe
    for %%A in ("release\Tamil Transliterator Setup 1.0.0.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo   Size: !sizeMB! MB
    )
)

if exist "release\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version: release\win-unpacked\Tamil Transliterator.exe
)

echo.
echo ========================================
echo ✅ BUILD SUCCESSFUL!
echo ========================================
echo.
echo 📦 Your distribution files are in the 'release' folder:
echo.
echo 1. INSTALLER: release\Tamil Transliterator Setup 1.0.0.exe
echo 2. PORTABLE: release\win-unpacked\Tamil Transliterator.exe
echo.
echo 🚀 Ready to distribute!
echo.

echo Opening release folder...
start explorer "release"

echo.
echo 💡 Note: Built to 'release' folder instead of 'dist'
echo This avoids Windows file lock issues.
echo.

pause
