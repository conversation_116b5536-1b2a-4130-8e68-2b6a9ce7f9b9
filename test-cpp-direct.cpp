#include <iostream>
#include <string>
#include "cpp/TranslateModule.h"

int main() {
    std::cout << "Testing C++ TranslateModule directly...\n\n";
    
    try {
        // Test basic translation
        std::string input = "vanakkam";
        std::cout << "Testing: " << input << std::endl;
        
        std::string result = TranslateModule::translateText(input);
        std::cout << "Result: " << result << std::endl;
        
        if (result == input) {
            std::cout << "WARNING: Translation returned input unchanged - possible engine issue\n";
        } else {
            std::cout << "SUCCESS: Translation worked!\n";
        }
        
        // Test suggestions
        std::cout << "\nTesting suggestions...\n";
        auto suggestions = TranslateModule::getSuggestions(input, 3);
        std::cout << "Suggestions count: " << suggestions.size() << std::endl;
        for (const auto& suggestion : suggestions) {
            std::cout << "  - " << suggestion << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "ERROR: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
