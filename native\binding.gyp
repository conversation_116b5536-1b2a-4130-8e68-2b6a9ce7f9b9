{"targets": [{"target_name": "transliterate_addon", "sources": ["src/transliterate_addon.cpp", "../cpp/TranslateModule.cpp"], "include_dirs": ["<!@(node -p \"require('node-addon-api').include\")", "../cpp"], "dependencies": ["<!(node -p \"require('node-addon-api').gyp\")"], "cflags!": ["-fno-exceptions"], "cflags_cc!": ["-fno-exceptions"], "xcode_settings": {"GCC_ENABLE_CPP_EXCEPTIONS": "YES", "CLANG_CXX_LIBRARY": "libc++", "MACOSX_DEPLOYMENT_TARGET": "10.7"}, "msvs_settings": {"VCCLCompilerTool": {"ExceptionHandling": 1, "AdditionalOptions": ["/std:c++17"], "RuntimeLibrary": 2}}, "conditions": [["OS=='win'", {"defines": ["_HAS_EXCEPTIONS=1", "NOMINMAX"], "msvs_settings": {"VCCLCompilerTool": {"ExceptionHandling": 1, "RuntimeLibrary": 2, "AdditionalOptions": ["/std:c++17"]}}}], ["OS=='linux'", {"cflags_cc": ["-std=c++17", "-fexceptions"]}], ["OS=='mac'", {"cflags_cc": ["-std=c++17", "-fexceptions"]}]]}]}