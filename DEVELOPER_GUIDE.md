# Tamil Transliterator Desktop - Developer Guide

## 🏗️ Architecture Overview

### Technology Stack
- **Frontend**: Electron (HTML/CSS/JavaScript)
- **Backend**: Node.js with native C++ modules
- **Build System**: node-gyp, electron-builder
- **Language**: C++ (engine), JavaScript (UI), TypeScript (types)

### Component Architecture
```
┌─────────────────────────────────────────┐
│           Electron Main Process         │
│  ┌─────────────────────────────────────┐ │
│  │        IPC Communication           │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │      Native Module Loader          │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│         Node.js Native Module           │
│  ┌─────────────────────────────────────┐ │
│  │       N-API Wrapper Layer          │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │      C++ Transliteration Engine    │ │
│  │  • SMCTransliterator (Primary)     │ │
│  │  • SimpleTransliterator (Fallback) │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        Electron Renderer Process        │
│  ┌─────────────────────────────────────┐ │
│  │           User Interface           │ │
│  │  • Input/Output Components         │ │
│  │  • Theme System                    │ │
│  │  • Event Handlers                  │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🛠️ Development Setup

### Prerequisites
```bash
# Node.js 18+
node --version

# Python 3.7+ (for node-gyp)
python --version

# Visual Studio Build Tools
# Download from: https://visualstudio.microsoft.com/downloads/
```

### Environment Setup
```bash
# Clone repository
git clone <repository-url>
cd tamil-transliterator-desktop

# Install dependencies
npm install

# Build native module
npm run build-native

# Start development
npm run dev
```

### Development Workflow
```bash
# For UI changes (hot reload)
npm run dev

# For C++ changes (requires rebuild)
npm run build-native
npm run dev

# For production build
npm run build
```

## 📁 Project Structure

### Core Directories
```
├── electron/                 # Electron application
│   ├── main.js              # Main process entry point
│   ├── preload.js           # Security layer
│   └── renderer/            # Frontend code
│       ├── index.html       # Main UI
│       ├── styles.css       # Styling
│       └── app.js           # Frontend logic
├── native/                  # Native module
│   ├── binding.gyp          # Build configuration
│   ├── package.json         # Module metadata
│   └── src/                 # C++ wrapper
│       ├── transliterate_addon.h
│       └── transliterate_addon.cpp
├── cpp/                     # C++ engine
│   ├── SMCTransliterator_Complete.h
│   ├── SimpleTransliterator.h
│   ├── TranslateModule.h
│   └── TranslateModule.cpp
└── scripts/                 # Build utilities
    └── build-native.js      # Native build script
```

## 🔧 Native Module Development

### C++ Engine Components

#### 1. SMCTransliterator (Primary Engine)
```cpp
// Advanced transliteration with ML features
class SMCTransliterator {
    std::string transliterate(const std::string& input);
    std::vector<std::string> getSuggestions(const std::string& input, int max);
    bool isInDictionary(const std::string& word);
};
```

#### 2. SimpleTransliterator (Fallback)
```cpp
// Reliable fallback with basic mappings
class SimpleTransliterator {
    std::string transliterate(const std::string& input);
    std::vector<std::string> getSuggestions(const std::string& input, int max);
    bool isInDictionary(const std::string& word);
};
```

#### 3. TranslateModule (Interface)
```cpp
// Unified interface with fallback logic
class TranslateModule {
    static std::string translateText(const std::string& input);
    static std::vector<std::string> getSuggestions(const std::string& input, int max);
    static std::string transliterateWithContext(const std::string& input, const std::string& context);
    static bool isWordInDictionary(const std::string& word);
};
```

### N-API Wrapper

#### Function Bindings
```cpp
// Main translation function
Napi::String TranslateText(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    std::string input = info[0].As<Napi::String>().Utf8Value();
    std::string result = TranslateModule::translateText(input);
    return Napi::String::New(env, result);
}

// Suggestions function
Napi::Array GetSuggestions(const Napi::CallbackInfo& info) {
    // Implementation details...
}
```

#### Module Registration
```cpp
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    exports.Set(Napi::String::New(env, "translateText"),
               Napi::Function::New(env, TranslateText));
    exports.Set(Napi::String::New(env, "getSuggestions"),
               Napi::Function::New(env, GetSuggestions));
    return exports;
}

NODE_API_MODULE(NODE_GYP_MODULE_NAME, Init)
```

## ⚡ Electron Application

### Main Process (main.js)
```javascript
// IPC handlers for native module communication
ipcMain.handle('translate-text', async (event, text) => {
    const nativeModule = require('../native/build/Release/transliterate_addon.node');
    return nativeModule.translateText(text);
});

ipcMain.handle('get-suggestions', async (event, text, maxSuggestions = 5) => {
    const nativeModule = require('../native/build/Release/transliterate_addon.node');
    return nativeModule.getSuggestions(text, maxSuggestions);
});
```

### Preload Script (preload.js)
```javascript
// Secure API exposure
contextBridge.exposeInMainWorld('electronAPI', {
    translateText: (text) => ipcRenderer.invoke('translate-text', text),
    getSuggestions: (text, max) => ipcRenderer.invoke('get-suggestions', text, max)
});
```

### Renderer Process (app.js)
```javascript
// Frontend application logic
class TamilTransliteratorApp {
    async translateText(text) {
        const result = await window.electronAPI.translateText(text);
        this.displayResult(result);
    }
    
    async getSuggestions(text) {
        const suggestions = await window.electronAPI.getSuggestions(text, 5);
        this.displaySuggestions(suggestions);
    }
}
```

## 🔨 Build System

### Native Module Build (binding.gyp)
```json
{
  "targets": [
    {
      "target_name": "transliterate_addon",
      "sources": [
        "src/transliterate_addon.cpp",
        "../cpp/TranslateModule.cpp"
      ],
      "include_dirs": [
        "<!@(node -p \"require('node-addon-api').include\")",
        "../cpp"
      ],
      "dependencies": [
        "<!(node -p \"require('node-addon-api').gyp\")"
      ],
      "cflags!": [ "-fno-exceptions" ],
      "cflags_cc!": [ "-fno-exceptions" ],
      "msvs_settings": {
        "VCCLCompilerTool": {
          "ExceptionHandling": 1,
          "AdditionalOptions": ["/std:c++17"]
        }
      }
    }
  ]
}
```

### Electron Builder Configuration
```json
{
  "build": {
    "appId": "com.tamiltools.transliterator",
    "productName": "Tamil Transliterator",
    "directories": {
      "output": "release"
    },
    "files": [
      "electron/**/*",
      "native/build/Release/*.node",
      "node_modules/**/*"
    ],
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

## 🧪 Testing

### Native Module Testing
```javascript
// Test script for native module
const transliterator = require('./native/build/Release/transliterate_addon.node');

console.log('Testing translation:');
console.log('vanakkam →', transliterator.translateText('vanakkam'));

console.log('Testing suggestions:');
console.log('Suggestions:', transliterator.getSuggestions('vanakkam', 3));
```

### Electron Testing
```javascript
// Integration testing
describe('Tamil Transliterator', () => {
    test('should translate basic words', async () => {
        const result = await window.electronAPI.translateText('vanakkam');
        expect(result).toBe('வணக்கம்');
    });
});
```

## 🚀 Deployment

### Build Process
```bash
# 1. Build native module
npm run build-native

# 2. Build Electron app
npm run build

# 3. Output files
# release/Tamil Transliterator Setup 1.0.0.exe (installer)
# release/win-unpacked/ (portable)
```

### Distribution
- **Installer**: Professional Windows installer with shortcuts
- **Portable**: Standalone executable in folder
- **Size**: ~150-200MB including all dependencies

## 🔍 Debugging

### Native Module Debugging
```bash
# Build with debug symbols
cd native
npx node-gyp configure --debug
npx node-gyp build

# Test directly
node -e "console.log(require('./build/Debug/transliterate_addon.node').translateText('test'))"
```

### Electron Debugging
```bash
# Development mode with DevTools
npm run dev

# Debug main process
electron --inspect=9229 .

# Debug renderer process
# Open DevTools in app (F12)
```

## 📈 Performance Optimization

### C++ Engine
- Use string references to avoid copying
- Implement caching for frequent translations
- Optimize dictionary lookups with hash maps
- Profile with tools like Valgrind or Visual Studio

### Electron App
- Minimize IPC calls
- Implement debouncing for real-time translation
- Use efficient DOM updates
- Optimize bundle size

## 🔒 Security Considerations

### Electron Security
- Context isolation enabled
- Node integration disabled in renderer
- Preload script for secure API exposure
- No remote module usage

### Native Module Security
- Input validation in C++ layer
- Memory management best practices
- Exception handling for stability
- No external network calls

---

This developer guide provides the foundation for understanding, modifying, and extending the Tamil Transliterator Desktop application. For specific implementation details, refer to the source code and inline comments.
