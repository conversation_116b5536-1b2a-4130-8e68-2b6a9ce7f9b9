cmake_minimum_required(VERSION 3.18.1)

project("translatemodule")

# Set C++17 standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add the cpp folder to include path
include_directories(${CMAKE_SOURCE_DIR}/../../../../../cpp)

add_library(
    translatemodule
    SHARED
    translate-lib.cpp
    ../../../../../cpp/TranslateModule.cpp
)

find_library(
    log-lib
    log
)

target_link_libraries(
    translatemodule
    ${log-lib}
)
