@echo off
echo ========================================
echo Tamil Transliterator Desktop - Development
echo ========================================
echo.

echo Starting development environment...
echo.

echo [1/3] Checking native module...
if not exist "native\build\Release\transliterate_addon.node" (
    echo Native module not found. Building...
    call npm run build-native
    if errorlevel 1 (
        echo ERROR: Failed to build native module
        pause
        exit /b 1
    )
) else (
    echo ✓ Native module found
)

echo [2/3] Installing dependencies (if needed)...
if not exist "node_modules" (
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
)

echo [3/3] Starting development mode...
echo.
echo The application will open in development mode.
echo - DevTools will be available (F12)
echo - Hot reload is enabled for UI changes
echo - For C++ changes, rebuild with: npm run build-native
echo.
echo Press Ctrl+C to stop the development server.
echo.

call npm run dev
