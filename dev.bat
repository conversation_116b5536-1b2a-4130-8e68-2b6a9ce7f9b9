@echo off
echo ========================================
echo Tamil Transliterator - Development
echo ========================================
echo.

echo Choose development target:
echo [1] Windows Desktop (Electron)
echo [2] Android (React Native)
echo [3] Metro bundler only
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto dev_windows
if "%choice%"=="2" goto dev_android
if "%choice%"=="3" goto dev_metro
echo Invalid choice. Exiting...
pause
exit /b 1

:dev_windows
echo.
echo Starting Windows Desktop Development Environment...
echo.

echo [1/3] Checking native module...
if not exist "native\build\Release\transliterate_addon.node" (
    echo Native module not found. Building...
    call npm run build-native
    if errorlevel 1 (
        echo ERROR: Failed to build native module
        pause
        exit /b 1
    )
) else (
    echo ✓ Native module found
)

echo [2/3] Installing dependencies (if needed)...
if not exist "node_modules" (
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
)

echo [3/3] Starting development mode...
echo.
echo The application will open in development mode.
echo - DevTools will be available (F12)
echo - Hot reload is enabled for UI changes
echo - For C++ changes, rebuild with: npm run build-native
echo.
echo Press Ctrl+C to stop the development server.
echo.

call npm run windows
goto end

:dev_android
echo.
echo Starting Android Development Environment...
echo.

echo [1/2] Installing dependencies (if needed)...
if not exist "node_modules" (
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
)

echo [2/2] Starting Android development...
echo.
echo Make sure you have:
echo - Android Studio installed
echo - Android device/emulator connected
echo - USB debugging enabled
echo.
echo Starting Metro bundler and Android app...
call npm run android
goto end

:dev_metro
echo.
echo Starting Metro Bundler Only...
echo.

echo [1/2] Installing dependencies (if needed)...
if not exist "node_modules" (
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
)

echo [2/2] Starting Metro bundler...
echo.
echo Metro bundler will start. Use this for:
echo - React Native development
echo - Hot reloading
echo - Debugging
echo.
call npm start

:end
