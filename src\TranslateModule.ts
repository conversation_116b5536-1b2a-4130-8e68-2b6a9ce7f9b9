import { NativeModules } from 'react-native';
import { translateTextJS } from './TranslateModuleFallback';

export interface TranslationResult {
  translatedText: string;
  moduleUsed: 'C++' | 'JavaScript';
  timestamp: number;
}

export interface SuggestionsResult {
  suggestions: string[];
  moduleUsed: 'C++' | 'JavaScript';
  timestamp: number;
}

export interface DictionaryCheckResult {
  isInDictionary: boolean;
  moduleUsed: 'C++' | 'JavaScript';
  timestamp: number;
}

const TranslateModuleBridge = NativeModules.TranslateModuleBridge ||
                              NativeModules.TranslateModule ||
                              NativeModules.TranslateModuleJava;

const TranslateModule = {
  translateText: async (input: string): Promise<TranslationResult> => {
    try {
      if (TranslateModuleBridge && typeof TranslateModuleBridge.translateText === 'function') {
        const result = await TranslateModuleBridge.translateText(input);
        return {
          translatedText: result,
          moduleUsed: 'C++',
          timestamp: Date.now(),
        };
      } else {
        const result = translateTextJS(input);
        return {
          translatedText: result,
          moduleUsed: 'JavaScript',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      const result = translateTextJS(input);
      return {
        translatedText: result,
        moduleUsed: 'JavaScript',
        timestamp: Date.now(),
      };
    }
  },

  getSuggestions: async (input: string, maxSuggestions: number = 5): Promise<SuggestionsResult> => {
    try {
      if (TranslateModuleBridge && typeof TranslateModuleBridge.getSuggestions === 'function') {
        const suggestions = await TranslateModuleBridge.getSuggestions(input, maxSuggestions);
        return {
          suggestions,
          moduleUsed: 'C++',
          timestamp: Date.now(),
        };
      } else {
        // Fallback: just return the basic translation as the only suggestion
        const result = translateTextJS(input);
        return {
          suggestions: [result],
          moduleUsed: 'JavaScript',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      const result = translateTextJS(input);
      return {
        suggestions: [result],
        moduleUsed: 'JavaScript',
        timestamp: Date.now(),
      };
    }
  },

  transliterateWithContext: async (input: string, context: string = ''): Promise<TranslationResult> => {
    try {
      if (TranslateModuleBridge && typeof TranslateModuleBridge.transliterateWithContext === 'function') {
        const result = await TranslateModuleBridge.transliterateWithContext(input, context);
        return {
          translatedText: result,
          moduleUsed: 'C++',
          timestamp: Date.now(),
        };
      } else {
        // Fallback: ignore context and use basic translation
        const result = translateTextJS(input);
        return {
          translatedText: result,
          moduleUsed: 'JavaScript',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      const result = translateTextJS(input);
      return {
        translatedText: result,
        moduleUsed: 'JavaScript',
        timestamp: Date.now(),
      };
    }
  },

  isWordInDictionary: async (word: string): Promise<DictionaryCheckResult> => {
    try {
      if (TranslateModuleBridge && typeof TranslateModuleBridge.isWordInDictionary === 'function') {
        const isInDictionary = await TranslateModuleBridge.isWordInDictionary(word);
        return {
          isInDictionary,
          moduleUsed: 'C++',
          timestamp: Date.now(),
        };
      } else {
        // Fallback: always return false since JS fallback has no dictionary
        return {
          isInDictionary: false,
          moduleUsed: 'JavaScript',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      return {
        isInDictionary: false,
        moduleUsed: 'JavaScript',
        timestamp: Date.now(),
      };
    }
  },
};

export default TranslateModule;