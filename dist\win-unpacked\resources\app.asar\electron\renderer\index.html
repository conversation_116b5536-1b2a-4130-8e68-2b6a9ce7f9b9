<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamil Transliterator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-icon">🔤</span>
                    Tamil Transliterator
                </h1>
                <div class="header-actions">
                    <button id="theme-toggle" class="icon-button" title="Toggle Dark Mode">
                        <span class="theme-icon">🌙</span>
                    </button>
                    <button id="settings-btn" class="icon-button" title="Settings">
                        <span>⚙️</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Input Section -->
            <section class="input-section">
                <div class="section-header">
                    <h2 class="section-title">English Input</h2>
                    <div class="section-actions">
                        <button id="paste-btn" class="action-button">
                            <span>📋</span> Paste
                        </button>
                        <button id="clear-btn" class="action-button">
                            <span>🗑️</span> Clear
                        </button>
                    </div>
                </div>
                <div class="input-container">
                    <textarea 
                        id="input-text" 
                        class="text-input" 
                        placeholder="Type English text here... (vanakkam, nandri, tamil)"
                        spellcheck="false"
                        autocomplete="off"
                        autocorrect="off"
                        autocapitalize="off"
                    ></textarea>
                    <div class="input-info">
                        <span id="char-count">0 characters</span>
                        <span id="word-count">0 words</span>
                    </div>
                </div>
            </section>

            <!-- Output Section -->
            <section class="output-section">
                <div class="section-header">
                    <h2 class="section-title">Tamil Output</h2>
                    <div class="section-actions">
                        <button id="copy-btn" class="action-button" disabled>
                            <span>📄</span> Copy
                        </button>
                        <button id="save-btn" class="action-button" disabled>
                            <span>💾</span> Save
                        </button>
                    </div>
                </div>
                <div class="output-container">
                    <div id="output-text" class="text-output">
                        Tamil translation will appear here...
                    </div>
                </div>
            </section>

            <!-- Suggestions Section -->
            <section class="suggestions-section" id="suggestions-section" style="display: none;">
                <div class="section-header">
                    <h3 class="section-title">Alternative Suggestions</h3>
                </div>
                <div class="suggestions-container" id="suggestions-container">
                    <!-- Suggestions will be populated here -->
                </div>
            </section>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span id="engine-status" class="status-item">
                        <span class="status-indicator"></span>
                        C++ Engine Ready
                    </span>
                </div>
                <div class="status-right">
                    <span id="translation-time" class="status-item"></span>
                </div>
            </div>
        </main>

        <!-- Help Panel -->
        <aside class="help-panel" id="help-panel">
            <div class="help-content">
                <h3>Keyboard Shortcuts</h3>
                <div class="shortcuts-list">
                    <div class="shortcut-item">
                        <kbd>Ctrl+V</kbd>
                        <span>Paste text</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl+A</kbd>
                        <span>Select all</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl+C</kbd>
                        <span>Copy output</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Ctrl+S</kbd>
                        <span>Save output</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>Escape</kbd>
                        <span>Clear input</span>
                    </div>
                    <div class="shortcut-item">
                        <kbd>F1</kbd>
                        <span>Toggle help</span>
                    </div>
                </div>
                
                <h3>Features</h3>
                <ul class="features-list">
                    <li>Real-time Tamil transliteration</li>
                    <li>Context-aware suggestions</li>
                    <li>1000+ word dictionary</li>
                    <li>High-performance C++ engine</li>
                    <li>Copy/paste support</li>
                    <li>File save/load</li>
                </ul>
            </div>
        </aside>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">Initializing Tamil Engine...</div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toast-container">
            <!-- Toasts will be added here dynamically -->
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
