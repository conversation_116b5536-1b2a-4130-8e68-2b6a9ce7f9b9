@echo off
echo ========================================
echo Tamil Transliterator Desktop - Dev Mode
echo ========================================
echo.

echo [1/3] Checking if native module exists...
if not exist "native\build\Release\transliterate_addon.node" (
    echo Native module not found. Building...
    call npm run build-native
    if errorlevel 1 (
        echo ERROR: Failed to build native module
        pause
        exit /b 1
    )
) else (
    echo ✓ Native module found
)

echo.
echo [2/3] Installing dependencies (if needed)...
if not exist "node_modules" (
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ✓ Dependencies already installed
)

echo.
echo [3/3] Starting Electron in development mode...
echo.
echo The application will open shortly...
echo Press Ctrl+C to stop the development server.
echo.

call npm run electron-dev
