package com.translatedemo;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableNativeArray;

public class TranslateModuleBridge extends ReactContextBaseJavaModule {
    
    public TranslateModuleBridge(ReactApplicationContext reactContext) {
        super(reactContext);
    }
    
    @Override
    public String getName() {
        return "TranslateModuleBridge";
    }
    
    @ReactMethod
    public void translateText(String input, Promise promise) {
        try {
            String result = TranslateModule.translateText(input);
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("TRANSLATE_ERROR", e.getMessage(), e);
        }
    }
    
    @ReactMethod
    public void getSuggestions(String input, int maxSuggestions, Promise promise) {
        try {
            String[] suggestions = TranslateModule.getSuggestions(input, maxSuggestions);
            WritableArray writableArray = new WritableNativeArray();
            for (String suggestion : suggestions) {
                writableArray.pushString(suggestion);
            }
            promise.resolve(writableArray);
        } catch (Exception e) {
            promise.reject("SUGGESTIONS_ERROR", e.getMessage(), e);
        }
    }
    
    @ReactMethod
    public void transliterateWithContext(String input, String context, Promise promise) {
        try {
            String result = TranslateModule.transliterateWithContext(input, context);
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("CONTEXT_TRANSLATE_ERROR", e.getMessage(), e);
        }
    }
    
    @ReactMethod
    public void isWordInDictionary(String word, Promise promise) {
        try {
            boolean result = TranslateModule.isWordInDictionary(word);
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("DICTIONARY_CHECK_ERROR", e.getMessage(), e);
        }
    }
}
