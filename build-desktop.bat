@echo off
echo ========================================
echo Tamil Transliterator Desktop Builder
echo ========================================
echo.

echo [1/4] Checking prerequisites...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found! Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found! Please install Python 3.7+ for node-gyp
    pause
    exit /b 1
)

echo ✓ Node.js found
echo ✓ Python found

echo.
echo [2/4] Installing dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo [3/4] Building native C++ module...
call npm run build-native
if errorlevel 1 (
    echo ERROR: Failed to build native module
    echo.
    echo Troubleshooting tips:
    echo - Ensure Visual Studio Build Tools are installed
    echo - Run as Administrator
    echo - Check that Windows SDK is available
    pause
    exit /b 1
)

echo.
echo [4/4] Building Electron application...
call npm run build-electron-win
if errorlevel 1 (
    echo ERROR: Failed to build Electron application
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ BUILD SUCCESSFUL!
echo ========================================
echo.
echo Output files:
echo - Installer: dist\Tamil Transliterator Setup 1.0.0.exe
echo - Portable:  dist\win-unpacked\Tamil Transliterator.exe
echo.
echo You can now distribute the installer or the portable version.
echo.
pause
