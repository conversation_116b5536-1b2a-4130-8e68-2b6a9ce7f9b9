#include "transliterate_addon.h"
#include <iostream>
#include <stdexcept>

// Ensure we have the right Node-API version
#define NAPI_VERSION 6

namespace TransliterateAddon {

    // Translate text function
    Napi::String TranslateText(const Napi::CallbackInfo& info) {
        Napi::Env env = info.Env();
        
        // Check arguments
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "String argument expected").ThrowAsJavaScriptException();
            return Napi::String::New(env, "");
        }
        
        try {
            std::string input = info[0].As<Napi::String>().Utf8Value();
            std::string result = TranslateModule::translateText(input);
            return Napi::String::New(env, result);
        } catch (const std::exception& e) {
            Napi::Error::New(env, std::string("Translation error: ") + e.what()).ThrowAsJavaScriptException();
            return Napi::String::New(env, "");
        }
    }

    // Get suggestions function
    Napi::Array GetSuggestions(const Napi::CallbackInfo& info) {
        Napi::Env env = info.Env();
        
        // Check arguments
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "String argument expected").ThrowAsJavaScriptException();
            return Napi::Array::New(env, 0);
        }
        
        try {
            std::string input = info[0].As<Napi::String>().Utf8Value();
            int maxSuggestions = 5;
            
            if (info.Length() >= 2 && info[1].IsNumber()) {
                maxSuggestions = info[1].As<Napi::Number>().Int32Value();
            }
            
            std::vector<std::string> suggestions = TranslateModule::getSuggestions(input, maxSuggestions);
            
            Napi::Array result = Napi::Array::New(env, suggestions.size());
            for (size_t i = 0; i < suggestions.size(); ++i) {
                result[i] = Napi::String::New(env, suggestions[i]);
            }
            
            return result;
        } catch (const std::exception& e) {
            Napi::Error::New(env, std::string("Suggestions error: ") + e.what()).ThrowAsJavaScriptException();
            return Napi::Array::New(env, 0);
        }
    }

    // Transliterate with context function
    Napi::String TransliterateWithContext(const Napi::CallbackInfo& info) {
        Napi::Env env = info.Env();
        
        // Check arguments
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "String argument expected").ThrowAsJavaScriptException();
            return Napi::String::New(env, "");
        }
        
        try {
            std::string input = info[0].As<Napi::String>().Utf8Value();
            std::string context = "";
            
            if (info.Length() >= 2 && info[1].IsString()) {
                context = info[1].As<Napi::String>().Utf8Value();
            }
            
            std::string result = TranslateModule::transliterateWithContext(input, context);
            return Napi::String::New(env, result);
        } catch (const std::exception& e) {
            Napi::Error::New(env, std::string("Context translation error: ") + e.what()).ThrowAsJavaScriptException();
            return Napi::String::New(env, "");
        }
    }

    // Check if word is in dictionary
    Napi::Boolean IsWordInDictionary(const Napi::CallbackInfo& info) {
        Napi::Env env = info.Env();
        
        // Check arguments
        if (info.Length() < 1 || !info[0].IsString()) {
            Napi::TypeError::New(env, "String argument expected").ThrowAsJavaScriptException();
            return Napi::Boolean::New(env, false);
        }
        
        try {
            std::string word = info[0].As<Napi::String>().Utf8Value();
            bool result = TranslateModule::isWordInDictionary(word);
            return Napi::Boolean::New(env, result);
        } catch (const std::exception& e) {
            Napi::Error::New(env, std::string("Dictionary check error: ") + e.what()).ThrowAsJavaScriptException();
            return Napi::Boolean::New(env, false);
        }
    }

    // Module initialization
    Napi::Object Init(Napi::Env env, Napi::Object exports) {
        exports.Set(Napi::String::New(env, "translateText"), 
                   Napi::Function::New(env, TranslateText));
        exports.Set(Napi::String::New(env, "getSuggestions"), 
                   Napi::Function::New(env, GetSuggestions));
        exports.Set(Napi::String::New(env, "transliterateWithContext"), 
                   Napi::Function::New(env, TransliterateWithContext));
        exports.Set(Napi::String::New(env, "isWordInDictionary"), 
                   Napi::Function::New(env, IsWordInDictionary));
        
        return exports;
    }

}

// Register the module using the correct macro
NAPI_MODULE(NODE_GYP_MODULE_NAME, TransliterateAddon::Init)
