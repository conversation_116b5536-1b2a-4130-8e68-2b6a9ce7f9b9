#include <napi.h>
#include <iostream>
#include <string>
#include "TranslateModule.h"

// Simple translateText function
Napi::String TranslateText(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsString()) {
        Napi::TypeError::New(env, "String argument expected").ThrowAsJavaScriptException();
        return Napi::String::New(env, "");
    }

    try {
        std::string input = info[0].As<Napi::String>().Utf8Value();
        std::string result = TranslateModule::translateText(input);
        return Napi::String::New(env, result);
    } catch (const std::exception& e) {
        std::cerr << "Translation error: " << e.what() << std::endl;
        return Napi::String::New(env, info[0].As<Napi::String>().Utf8Value());
    }
}

// Simple getSuggestions function
Napi::Array GetSuggestions(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();

    if (info.Length() < 1 || !info[0].IsString()) {
        return Napi::Array::New(env, 0);
    }

    try {
        std::string input = info[0].As<Napi::String>().Utf8Value();
        int maxSuggestions = 5;

        if (info.Length() >= 2 && info[1].IsNumber()) {
            maxSuggestions = info[1].As<Napi::Number>().Int32Value();
        }

        std::vector<std::string> suggestions = TranslateModule::getSuggestions(input, maxSuggestions);

        Napi::Array result = Napi::Array::New(env, suggestions.size());
        for (size_t i = 0; i < suggestions.size(); ++i) {
            result[i] = Napi::String::New(env, suggestions[i]);
        }

        return result;
    } catch (const std::exception& e) {
        std::cerr << "Suggestions error: " << e.what() << std::endl;
        return Napi::Array::New(env, 0);
    }
}

// Module initialization
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    exports.Set(Napi::String::New(env, "translateText"),
               Napi::Function::New(env, TranslateText));
    exports.Set(Napi::String::New(env, "getSuggestions"),
               Napi::Function::New(env, GetSuggestions));

    return exports;
}

NODE_API_MODULE(NODE_GYP_MODULE_NAME, Init)
