/* CSS Variables for theming */
:root {
    --primary-color: #007AFF;
    --primary-hover: #0056CC;
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --border-color: #e1e5e9;
    --text-primary: #1d1d1f;
    --text-secondary: #6e6e73;
    --text-muted: #8e8e93;
    --success-color: #34c759;
    --warning-color: #ff9500;
    --error-color: #ff3b30;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --border-radius-large: 12px;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', <PERSON><PERSON><PERSON>, 'Courier New', monospace;
    --transition: all 0.2s ease;
}

/* Dark theme */
[data-theme="dark"] {
    --background-color: #1c1c1e;
    --surface-color: #2c2c2e;
    --border-color: #38383a;
    --text-primary: #ffffff;
    --text-secondary: #ebebf5;
    --text-muted: #ebebf599;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    user-select: none;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* Header */
.app-header {
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: 16px 24px;
    flex-shrink: 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.app-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.title-icon {
    font-size: 28px;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.icon-button {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
    font-size: 16px;
}

.icon-button:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

/* Main content */
.main-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Sections */
.input-section,
.output-section,
.suggestions-section {
    margin-bottom: 32px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    gap: 8px;
}

.action-button {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 16px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Input container */
.input-container {
    position: relative;
}

.text-input {
    width: 100%;
    min-height: 120px;
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-large);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 16px;
    font-family: var(--font-family);
    line-height: 1.5;
    resize: vertical;
    transition: var(--transition);
    user-select: text;
}

.text-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.text-input::placeholder {
    color: var(--text-muted);
}

.input-info {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

/* Output container */
.output-container {
    position: relative;
}

.text-output {
    width: 100%;
    min-height: 100px;
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-large);
    background-color: var(--surface-color);
    color: var(--text-primary);
    font-size: 18px;
    font-family: var(--font-family-mono);
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    user-select: text;
    cursor: text;
}

.text-output:empty::before {
    content: attr(data-placeholder);
    color: var(--text-muted);
    font-style: italic;
}

/* Suggestions */
.suggestions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.suggestion-chip {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 6px 12px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    color: var(--text-secondary);
    user-select: none;
}

.suggestion-chip:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Status bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background-color: var(--surface-color);
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-muted);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
}

/* Help panel */
.help-panel {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background-color: var(--surface-color);
    border-left: 1px solid var(--border-color);
    padding: 24px;
    overflow-y: auto;
    transition: right 0.3s ease;
    z-index: 1000;
}

.help-panel.open {
    right: 0;
}

.help-content h3 {
    margin-bottom: 16px;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
}

.shortcuts-list {
    margin-bottom: 24px;
}

.shortcut-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.shortcut-item:last-child {
    border-bottom: none;
}

kbd {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 11px;
    font-family: var(--font-family-mono);
    color: var(--text-secondary);
}

.features-list {
    list-style: none;
}

.features-list li {
    padding: 4px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 16px;
}

.features-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.toast {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    box-shadow: var(--shadow-medium);
    max-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        padding: 16px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .help-panel {
        width: 100%;
        right: -100%;
    }
}
