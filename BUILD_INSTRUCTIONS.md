# Tamil Transliterator Desktop - Build Instructions

## 🎯 Quick Start

### One-Command Build
```bash
npm run build
```
This will:
1. Build the native C++ module
2. Package the Electron application
3. Create installer and portable versions
4. Output files to `release/` folder

## 📋 Prerequisites

### Required Software
- **Node.js 18+**: [Download here](https://nodejs.org/)
- **Python 3.7+**: Required for node-gyp
- **Visual Studio Build Tools**: For C++ compilation

### Windows Build Tools Setup
```bash
# Option 1: Install build tools automatically
npm install -g windows-build-tools

# Option 2: Manual installation
# Download Visual Studio Community 2022
# Select "Desktop development with C++" workload
# Ensure Windows 10/11 SDK is included
```

### Verify Prerequisites
```bash
# Check Node.js
node --version
# Should show v18.0.0 or higher

# Check Python
python --version
# Should show Python 3.7.0 or higher

# Check build tools
npm config get msvs_version
# Should show 2022 or 2019
```

## 🔧 Step-by-Step Build Process

### Step 1: Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd tamil-transliterator-desktop

# Install dependencies
npm install
```

### Step 2: Build Native Module
```bash
# Build C++ native module
npm run build-native

# Verify build
ls native/build/Release/
# Should contain: transliterate_addon.node
```

### Step 3: Test Native Module
```bash
# Test the native module
node -e "console.log(require('./native/build/Release/transliterate_addon.node').translateText('vanakkam'))"
# Should output: வணக்கம்
```

### Step 4: Build Electron Application
```bash
# Build complete application
npm run build

# Check output
ls release/
# Should contain:
# - Tamil Transliterator Setup 1.0.0.exe
# - win-unpacked/
```

## 🛠️ Manual Build Steps

### Native Module (Manual)
```bash
cd native

# Install native dependencies
npm install

# Clean previous build
npx node-gyp clean

# Configure build
npx node-gyp configure --msvs_version=2022

# Build
npx node-gyp build

# Verify
ls build/Release/transliterate_addon.node
```

### Electron App (Manual)
```bash
# Return to root directory
cd ..

# Build with electron-builder
npx electron-builder --win --config.directories.output=release

# Check output
ls release/
```

## 🔍 Troubleshooting Build Issues

### Common Problems

#### 1. Node-gyp Build Fails
```bash
# Error: MSBuild.exe not found
# Solution: Install Visual Studio Build Tools

# Error: Python not found
# Solution: Install Python 3.7+
npm config set python python3.exe

# Error: Permission denied
# Solution: Run as Administrator
```

#### 2. Native Module Not Found
```bash
# Error: Cannot find module 'transliterate_addon.node'
# Solution: Rebuild native module
npm run build-native

# Verify file exists
ls native/build/Release/transliterate_addon.node
```

#### 3. Electron Build Fails
```bash
# Error: File locks or permission issues
# Solution: Close all applications, run as Administrator

# Error: Antivirus blocking
# Solution: Temporarily disable antivirus during build
```

#### 4. C++ Compilation Errors
```bash
# Error: C++ standard version
# Solution: Ensure C++17 support in binding.gyp

# Error: Missing headers
# Solution: Check include paths in binding.gyp
```

## 🎯 Build Configurations

### Development Build
```bash
# Fast build for development
npm run build-native
npm run dev
```

### Production Build
```bash
# Full production build
npm run build
```

### Debug Build
```bash
# Build with debug symbols
cd native
npx node-gyp configure --debug
npx node-gyp build
cd ..
npm run dev
```

## 📦 Output Files

### Release Folder Structure
```
release/
├── Tamil Transliterator Setup 1.0.0.exe    # Windows installer
├── Tamil Transliterator Setup 1.0.0.exe.blockmap
├── latest.yml                               # Update metadata
├── builder-debug.yml                        # Build debug info
├── builder-effective-config.yaml           # Build configuration
└── win-unpacked/                           # Portable version
    ├── Tamil Transliterator.exe            # Main executable
    ├── resources/                           # App resources
    │   └── app.asar                        # Packaged app
    ├── locales/                            # Electron locales
    └── [various DLLs and support files]
```

### File Sizes (Approximate)
- **Installer**: 100-150 MB
- **Portable**: 150-200 MB
- **Native Module**: 1-5 MB

## 🚀 Distribution

### Testing Before Distribution
```bash
# Test installer
# 1. Run the .exe installer
# 2. Install on clean Windows machine
# 3. Verify app starts and translates correctly

# Test portable version
# 1. Copy win-unpacked folder to different location
# 2. Run Tamil Transliterator.exe
# 3. Verify functionality
```

### Distribution Checklist
- [ ] Build completes without errors
- [ ] Native module loads correctly
- [ ] Translation works for test words
- [ ] Installer creates shortcuts
- [ ] Portable version runs independently
- [ ] No missing dependencies
- [ ] Windows Defender warnings handled

## 🔄 Automated Build

### CI/CD Pipeline (Example)
```yaml
# .github/workflows/build.yml
name: Build Tamil Transliterator
on: [push, pull_request]

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: npm install
      
      - name: Build native module
        run: npm run build-native
      
      - name: Build Electron app
        run: npm run build
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: tamil-transliterator-windows
          path: release/
```

### Local Automation Script
```bash
# build-all.bat
@echo off
echo Building Tamil Transliterator...
call npm install
call npm run build-native
call npm run build
echo Build complete! Check release/ folder.
pause
```

## 📊 Build Performance

### Typical Build Times
- **Native module**: 30-60 seconds
- **Electron packaging**: 2-5 minutes
- **Total build time**: 3-6 minutes

### Optimization Tips
- Use SSD for faster I/O
- Close unnecessary applications
- Use latest Node.js version
- Enable Windows Developer Mode
- Exclude build folders from antivirus

## 🔒 Security Notes

### Code Signing (Optional)
```bash
# For commercial distribution, consider code signing
# 1. Purchase code signing certificate
# 2. Configure electron-builder with certificate
# 3. Sign both installer and executable
```

### Build Environment Security
- Use clean build environment
- Verify all dependencies
- Scan output files for malware
- Use official Node.js and Python installations

---

Following these instructions should result in a successful build of the Tamil Transliterator Desktop application. If you encounter issues not covered here, check the troubleshooting section or refer to the developer guide for more detailed information.
