# C++ Integration with React Native: Step-by-Step Guide

This document explains how C++ native modules are integrated with React Native applications, using our Letter Shifter app as an example.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Project Structure](#project-structure)
3. [Step 1: C++ Core Implementation](#step-1-c-core-implementation)
4. [Step 2: Android Integration (JNI)](#step-2-android-integration-jni)
5. [Step 3: React Native JavaScript Bridge](#step-3-react-native-javascript-bridge)
6. [Step 4: Build Configuration](#step-4-build-configuration)
7. [Step 5: Module Registration](#step-5-module-registration)
8. [Testing & Verification](#testing--verification)
9. [Troubleshooting](#troubleshooting)

## Overview

React Native C++ integration allows you to:
- Execute high-performance algorithms in native C++
- Access platform-specific APIs and libraries
- Optimize computationally intensive operations

**Architecture Flow:**
```
JavaScript/TypeScript (React Native)
           ↓
    Platform Bridge Layer
           ↓
         JNI (Android)
           ↓
         C++ Core
```

## Project Structure

```
TranslateDemo/
├── cpp/                          # C++ source files
│   ├── TranslateModule.h         # C++ header file
│   └── TranslateModule.cpp       # C++ implementation
├── android/
│   └── app/src/main/
│       ├── cpp/
│       │   └── translate-lib.cpp # JNI wrapper
│       └── java/com/translatedemo/
│           ├── TranslateModule.java
│           ├── TranslateModuleBridge.java
│           └── TranslatePackage.java
└── src/
    ├── TranslateModule.ts        # React Native interface
    └── TranslateModuleFallback.ts # JavaScript fallback
```

## Step 1: C++ Core Implementation

### 1.1 Create the Header File (`cpp/TranslateModule.h`)

```cpp
#ifndef TRANSLATEMODULE_H
#define TRANSLATEMODULE_H

#include <string>

class TranslateModule {
public:
    static std::string translateText(const std::string& input);
private:
    static char incrementChar(char c);
};

#endif // TRANSLATEMODULE_H
```

**Key Points:**
- Use `#ifndef` guards to prevent multiple inclusions
- Declare public static methods for external access
- Keep implementation details private

### 1.2 Implement the C++ Logic (`cpp/TranslateModule.cpp`)

```cpp
#include "TranslateModule.h"

std::string TranslateModule::translateText(const std::string& input) {
    std::string result;
    result.reserve(input.length());
    
    for (char c : input) {
        result += incrementChar(c);
    }
    
    return result;
}

char TranslateModule::incrementChar(char c) {
    if (c >= 'a' && c <= 'z') {
        return (c == 'z') ? 'a' : (c + 1);
    } else if (c >= 'A' && c <= 'Z') {
        return (c == 'Z') ? 'A' : (c + 1);
    }
    return c;
}
```

**Key Points:**
- Use `std::string` for text handling
- Implement character-by-character processing
- Handle edge cases (z→a, Z→A)

## Step 2: Android Integration (JNI)

### 2.1 Create JNI Wrapper (`android/app/src/main/cpp/translate-lib.cpp`)

```cpp
#include <jni.h>
#include <string>
#include "../../../../../../../cpp/TranslateModule.h"

extern "C" JNIEXPORT jstring JNICALL
Java_com_translatedemo_TranslateModule_translateTextNative(
    JNIEnv *env,
    jobject /* this */,
    jstring input) {
    
    const char *nativeString = env->GetStringUTFChars(input, 0);
    std::string cppInput(nativeString);
    env->ReleaseStringUTFChars(input, nativeString);
    
    std::string result = TranslateModule::translateText(cppInput);
    
    return env->NewStringUTF(result.c_str());
}
```

**Key Points:**
- `extern "C"` prevents C++ name mangling
- `JNIEXPORT` makes function visible to Java
- Convert between Java strings and C++ strings
- Always release JNI string resources

### 2.2 Create Java Bridge (`android/app/src/main/java/com/translatedemo/TranslateModule.java`)

```java
package com.translatedemo;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;

public class TranslateModule extends ReactContextBaseJavaModule {
    static {
        System.loadLibrary("translate-lib");
    }

    public TranslateModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "TranslateModule";
    }

    @ReactMethod
    public void translateText(String input, Promise promise) {
        try {
            String result = translateTextNative(input);
            promise.resolve(result);
        } catch (Exception e) {
            promise.reject("TRANSLATE_ERROR", e);
        }
    }

    private native String translateTextNative(String input);
}
```

**Key Points:**
- `System.loadLibrary()` loads the compiled C++ library
- `@ReactMethod` exposes methods to React Native
- Use `Promise` for asynchronous operations
- Handle exceptions gracefully

### 2.3 Configure CMake Build (`android/app/build.gradle`)

```gradle
android {
    // ...existing code...
    
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
    
    defaultConfig {
        // ...existing code...
        
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
    }
}
```

### 2.4 Create CMakeLists.txt (`android/app/src/main/cpp/CMakeLists.txt`)

```cmake
cmake_minimum_required(VERSION 3.22.1)

project("translate-lib")

# Add C++ source files
add_library(
    translate-lib
    SHARED
    translate-lib.cpp
    ../../../../../../../cpp/TranslateModule.cpp
)

# Find required libraries
find_library(log-lib log)

# Link libraries
target_link_libraries(
    translate-lib
    ${log-lib}
)

# Include directories
target_include_directories(
    translate-lib PRIVATE
    ../../../../../../../cpp
)
```

## Step 3: React Native JavaScript Bridge

### 3.1 Create TypeScript Interface (`src/TranslateModule.ts`)

```typescript
import { NativeModules } from 'react-native';
import { translateTextJS } from './TranslateModuleFallback';

export interface TranslationResult {
  translatedText: string;
  moduleUsed: 'C++' | 'JavaScript';
  timestamp: number;
}

const TranslateModuleBridge = NativeModules.TranslateModuleBridge ||
                              NativeModules.TranslateModule ||
                              NativeModules.TranslateModuleJava;

const TranslateModule = {
  translateText: async (input: string): Promise<TranslationResult> => {
    try {
      if (TranslateModuleBridge && typeof TranslateModuleBridge.translateText === 'function') {
        const result = await TranslateModuleBridge.translateText(input);
        return {
          translatedText: result,
          moduleUsed: 'C++',
          timestamp: Date.now(),
        };
      } else {
        const result = translateTextJS(input);
        return {
          translatedText: result,
          moduleUsed: 'JavaScript',
          timestamp: Date.now(),
        };
      }
    } catch (error) {
      const result = translateTextJS(input);
      return {
        translatedText: result,
        moduleUsed: 'JavaScript',
        timestamp: Date.now(),
      };
    }
  },
};

export default TranslateModule;
```

### 4.2 Create JavaScript Fallback (`src/TranslateModuleFallback.ts`)

```typescript
function translateTextJS(input: string): string {
  return input.split('').map(char => {
    if (char >= 'a' && char <= 'z') {
      return char === 'z' ? 'a' : String.fromCharCode(char.charCodeAt(0) + 1);
    } else if (char >= 'A' && char <= 'Z') {
      return char === 'Z' ? 'A' : String.fromCharCode(char.charCodeAt(0) + 1);
    }
    return char;
  }).join('');
}

export { translateTextJS };
```

## Step 5: Build Configuration

### 5.1 Android Build Configuration

Add to `android/app/build.gradle`:
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17"
                arguments "-DANDROID_STL=c++_shared"
            }
        }
        
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a", "x86", "x86_64"
        }
    }
    
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
}
```

## Step 5: Module Registration

### 5.1 Android Module Registration

Create `android/app/src/main/java/com/translatedemo/TranslatePackage.java`:
```java
package com.translatedemo;

import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.uimanager.ViewManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TranslatePackage implements ReactPackage {
    @Override
    public List<NativeModule> createNativeModules(ReactApplicationContext reactContext) {
        List<NativeModule> modules = new ArrayList<>();
        modules.add(new TranslateModule(reactContext));
        return modules;
    }

    @Override
    public List<ViewManager> createViewManagers(ReactApplicationContext reactContext) {
        return Collections.emptyList();
    }
}
```

Register in `MainApplication.kt`:
```kotlin
override fun getPackages(): List<ReactPackage> {
    return PackageList(this).packages.apply {
        add(TranslatePackage())
    }
}
```

## Testing & Verification

### 1. Build the Project
```bash
# Android
npx react-native run-android
```

### 2. Verify Native Module Loading
Check React Native logs for:
```
Using native TranslateModule
```

### 3. Test Functionality
- Input: "hello"
- Expected Output: "ifmmp"
- Verify real-time translation works

## Troubleshooting

### Common Android Issues

**CMake Build Fails:**
```bash
# Clean and rebuild
cd android
./gradlew clean
cd ..
npx react-native run-android
```

**JNI Method Not Found:**
- Verify method signature matches exactly
- Check package names in Java files
- Ensure `System.loadLibrary()` is called

**Library Not Found:**
- Check CMakeLists.txt paths
- Verify C++ files are included in build
- Check ABI filters match target devices

### General Debugging

**Enable Native Module Debugging:**
```typescript
// Add to App.tsx
console.log('Available modules:', Object.keys(NativeModules));
```

**Check Module Registration:**
```bash
# Android logs
adb logcat | grep "ReactNative"
```

