# Tamil Transliterator

<div align="center">

![Tamil Transliterator Logo](https://img.shields.io/badge/Tamil-Transliterator-FF6B35?style=for-the-badge&logo=language&logoColor=white)

**A professional cross-platform Tamil transliteration application**  
*Convert English text to Tamil script in real-time*

[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Android-blue?style=flat-square)](https://github.com/your-repo)
[![Version](https://img.shields.io/badge/Version-1.0.0-green?style=flat-square)](https://github.com/your-repo/releases)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=flat-square)](LICENSE)
[![React Native](https://img.shields.io/badge/React%20Native-0.79.2-61DAFB?style=flat-square&logo=react)](https://reactnative.dev/)
[![Electron](https://img.shields.io/badge/Electron-28.0.0-47848F?style=flat-square&logo=electron)](https://electronjs.org/)

[📥 Download](#-installation) • [📖 Documentation](#-table-of-contents) • [🚀 Quick Start](#-quick-start) • [🤝 Contributing](#-contributing)

</div>

---

## 📋 Table of Contents

- [🎯 Overview](#-overview)
- [✨ Features](#-features)
- [📱 Platforms](#-platforms)
- [🎯 Supported Transliterations](#-supported-transliterations)
- [💻 System Requirements](#-system-requirements)
- [📥 Installation](#-installation)
- [🚀 Quick Start](#-quick-start)
- [📖 Usage Guide](#-usage-guide)
- [⚡ Development](#-development)
- [🔧 Building from Source](#-building-from-source)
- [🐛 Troubleshooting](#-troubleshooting)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [🙏 Acknowledgments](#-acknowledgments)

---

## 🎯 Overview

**Tamil Transliterator** is a professional-grade cross-platform application that converts English text to Tamil script in real-time. Built with modern technologies and powered by a high-performance C++ transliteration engine, it delivers fast, accurate, and reliable Tamil text conversion across multiple platforms.

### 🌟 Why Tamil Transliterator?

- **🚀 Real-time Translation**: See Tamil text appear as you type English
- **⚡ High Performance**: Native C++ engine for lightning-fast processing
- **🎯 Accurate Results**: Smart transliteration with context awareness
- **📱 Multi-Platform**: Same experience on Windows Desktop and Android
- **🔒 Privacy First**: Works completely offline, no data sent to servers
- **💡 Smart Suggestions**: Multiple transliteration options for ambiguous words

---

## ✨ Features

### 🔥 Core Functionality
- **Real-time Tamil Transliteration** - Type English and see Tamil instantly
- **Smart Suggestions** - Get alternative translations with context awareness  
- **High Performance** - C++ engine for fast, accurate translations
- **50+ Word Dictionary** - Common Tamil words with perfect translations
- **Offline Operation** - No internet connection required
- **Context-Aware Translation** - Improved accuracy based on surrounding text

### 🎨 User Interface
- **Modern Design** - Clean, professional interface optimized for each platform
- **Dark/Light Themes** - Automatic theme switching based on system preferences
- **Real-time Feedback** - Character and word counting with status indicators
- **Responsive Layout** - Adapts to different screen sizes and orientations
- **Accessibility** - Full keyboard navigation and screen reader support

### 🖥️ Desktop Features (Windows)
- **Keyboard Shortcuts** - Full keyboard support for power users
- **Copy/Paste Integration** - Seamless clipboard operations
- **File Operations** - Save translations to text files
- **Menu System** - Standard Windows menus and shortcuts
- **System Integration** - Native Windows look and feel

### 📱 Mobile Features (Android)
- **Touch-Optimized** - Gesture-friendly interface designed for mobile
- **Clipboard Integration** - Easy copy/paste functionality
- **External Keyboard Support** - Productivity shortcuts for external keyboards
- **Suggestions** - Swipe through alternative translations
- **Mobile-First Design** - Optimized for touch interaction

---

## 📱 Platforms

<table>
<tr>
<td width="50%">

### 🖥️ **Windows Desktop**
- **Technology**: Electron + Native C++ (N-API)
- **UI Framework**: React + TypeScript
- **Engine**: High-performance C++ transliterator
- **Distribution**: Installer (.exe) + Portable version
- **Integration**: Full Windows desktop integration

</td>
<td width="50%">

### 📱 **Android Mobile**
- **Technology**: React Native + Native C++ (JNI)
- **UI Framework**: React Native + TypeScript  
- **Engine**: Same C++ engine as desktop
- **Distribution**: APK file for direct installation
- **Integration**: Native Android experience

</td>
</tr>
</table>

---

## 🎯 Supported Transliterations

### 📝 **Common Words & Phrases**
```
English → Tamil
vanakkam → வணக்கம் (Hello)
nandri → நன்றி (Thank you)
tamil → தமிழ் (Tamil)
amma → அம்மா (Mother)
appa → அப்பா (Father)
computer → கம்ப்யூட்டர் (Computer)
mobile → மொபைல் (Mobile)
school → பள்ளி (School)
book → புத்தகம் (Book)
water → தண்ணீர் (Water)
```

### 🔤 **Character Transliteration**
- **Vowels**: `a`, `aa`, `i`, `ii`, `u`, `uu`, `e`, `ee`, `ai`, `o`, `oo`, `au`
- **Consonants**: `k`, `g`, `ch`, `j`, `t`, `d`, `n`, `th`, `p`, `b`, `m`, `y`, `r`, `l`, `v`, `s`, `h`
- **Special Characters**: `zh`, `ll`, `rr`, `nn`

### 🎯 **Advanced Features**
- **Context-aware conversion** for accurate word formation
- **Alternative suggestions** for ambiguous transliterations
- **Dictionary-based validation** for common Tamil words
- **Real-time feedback** with character and word counting

---

## 💻 System Requirements

### 🖥️ **Windows Desktop**
| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **OS** | Windows 10 (64-bit) | Windows 11 |
| **RAM** | 4GB | 8GB |
| **Storage** | 500MB free space | 1GB free space |
| **Processor** | Intel/AMD 64-bit | Modern Intel/AMD |

### 📱 **Android Mobile**
| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **OS** | Android 7.0 (API 24) | Android 10+ |
| **RAM** | 2GB | 4GB |
| **Storage** | 100MB free space | 200MB free space |
| **Architecture** | ARM64, ARMv7, x86, x86_64 | ARM64 |

### 🛠️ **Development Environment**
| Component | Version | Purpose |
|-----------|---------|---------|
| **Node.js** | 18.x or later | JavaScript runtime |
| **Android SDK** | API level 24+ with NDK | Android development |
| **Visual Studio** | 2019 or later (Windows) | C++ compilation |
| **Python** | 3.8+ | Native module compilation |

---

## 📥 Installation

### 🖥️ **Windows Desktop**

#### **Option 1: Installer (Recommended)**
1. **Download** the installer from [Releases](release/windows/)
   ```
   📁 Tamil Transliterator Setup 1.0.0.exe (~150MB)
   ```
2. **Run the installer** and follow the setup wizard
3. **Launch** from Start Menu or Desktop shortcut

#### **Option 2: Portable Version**
1. **Download** the portable version from [Releases](release/windows/)
   ```
   📁 win-unpacked/ folder (~200MB)
   ```
2. **Extract** to your preferred location
3. **Run** `Tamil Transliterator.exe` directly

### 📱 **Android Mobile**

#### **APK Installation**
1. **Download** the APK file from [Releases](release/android/)
   ```
   📁 Tamil-Transliterator-1.0.0.apk (~50MB)
   ```
2. **Enable** "Install from Unknown Sources" in Android settings:
   - Go to **Settings > Security**
   - Enable **"Unknown Sources"** or **"Install unknown apps"**
3. **Install** the APK file
4. **Launch** from your app drawer

---

## 🚀 Quick Start

### 🎮 **How to Use**

#### **Windows Desktop**
1. **Launch** the application from Start Menu
2. **Type English text** in the input field
3. **See Tamil translation** appear in real-time
4. **Use keyboard shortcuts** for quick operations:
   - `Ctrl+V`: Paste text
   - `Ctrl+C`: Copy translation
   - `Ctrl+A`: Select all
   - `Escape`: Clear input
5. **Save translations** using File menu

#### **Android Mobile**
1. **Open** the Tamil Transliterator app
2. **Type English words** in the input field
3. **See instant Tamil translation** in the output area
4. **Tap suggestions** to use alternative translations
5. **Use copy/paste buttons** for clipboard operations
6. **Connect external keyboard** for enhanced productivity

### 🔌 **External Keyboard Setup (Android)**
1. **USB OTG**: Connect keyboard → OTG adapter → Phone
2. **Bluetooth**: Pair keyboard in phone's Bluetooth settings
3. **Test**: Open app and try keyboard shortcuts

---

## 📖 Usage Guide

### ⌨️ **Keyboard Shortcuts**

#### **Windows Desktop**
| Shortcut | Action |
|----------|--------|
| `Ctrl+V` | Paste text into input field |
| `Ctrl+C` | Copy Tamil output |
| `Ctrl+A` | Select all text |
| `Ctrl+S` | Save translation to file |
| `Escape` | Clear all fields |
| `F1` | Show help |

#### **Android Mobile (External Keyboard)**
| Shortcut | Action |
|----------|--------|
| `Ctrl+V` | Paste text |
| `Ctrl+C` | Copy Tamil output |
| `Ctrl+A` | Select all text |
| `Escape` | Clear all fields |

### 🎯 **Translation Examples**

#### **Basic Words**
```
Type: vanakkam
Get: வணக்கம்

Type: nandri
Get: நன்றி

Type: tamil
Get: தமிழ்
```

#### **Sentences**
```
Type: vanakkam nanba
Get: வணக்கம் நண்பா

Type: nandri amma
Get: நன்றி அம்மா
```

#### **Advanced Features**
- **Alternative suggestions**: Type ambiguous words to see multiple options
- **Context awareness**: Better accuracy for longer sentences
- **Real-time feedback**: Character and word count updates

---

## ⚡ Development

### 🛠️ **Prerequisites**

#### **Required Software**
```bash
# Node.js (18.x or later)
node --version

# npm (comes with Node.js)
npm --version

# Git
git --version
```

#### **Windows Desktop Development**
```bash
# Visual Studio 2019 or later
# Python 3.8+ (for node-gyp)
python --version

# Windows SDK
# Visual C++ Build Tools
```

#### **Android Development**
```bash
# Android Studio
# Android SDK (API level 24+)
# Android NDK
# Java 11+
java --version
```

### 🚀 **Quick Development Setup**

#### **1. Clone Repository**
```bash
git clone <repository-url>
cd tamil-transliterator
```

#### **2. Install Dependencies**
```bash
# Install all dependencies
npm install

# This will automatically build the native module
```

#### **3. Environment Variables (Android)**
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools

# macOS/Linux
export ANDROID_HOME=/path/to/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
```

#### **4. Start Development**
```bash
# Windows Desktop Development
npm run windows

# Android Development (device/emulator required)
npm run android

# Metro Bundler Only
npm start
```

### 📱 **Development Commands**

| Command | Platform | Description |
|---------|----------|-------------|
| `npm run windows` | 🖥️ Desktop | Start Windows development mode |
| `npm run android` | 📱 Mobile | Start Android development mode |
| `npm start` | 📱 Mobile | Start Metro bundler only |
| `npm run build-desktop` | 🖥️ Desktop | Build Windows installer |
| `npm run build-android` | 📱 Mobile | Build Android APK |
| `npm run build-native` | 🔧 Both | Build C++ native module |
| `npm run lint` | 🔧 Both | Code linting |
| `npm test` | 🔧 Both | Run tests |

### 🎯 **Interactive Scripts**
```bash
# Interactive development script
./dev.bat

# Interactive build script
./build.bat
```

---

## 🔧 Building from Source

### 🖥️ **Windows Desktop Build**

#### **Development Build**
```bash
# Start development mode
npm run windows
```

#### **Production Build**
```bash
# Build installer and portable version
npm run build-desktop

# Output location
# release/windows/Tamil Transliterator Setup 1.0.0.exe
# release/windows/win-unpacked/
```

### 📱 **Android Build**

#### **Development Build**
```bash
# Install on connected device/emulator
npm run android
```

#### **Production Build**
```bash
# Build release APK
npm run build-android

# Output location
# android/app/build/outputs/apk/release/app-release.apk
```

### 🔧 **Native Module Build**
```bash
# Build C++ native module only
npm run build-native

# Verbose output for debugging
npm run build-native -- --verbose
```

---

## 🐛 Troubleshooting

### 🖥️ **Windows Desktop Issues**

#### **"Windows protected your PC" message**
```
Solution:
1. Click "More info"
2. Click "Run anyway"
3. This is normal for unsigned applications
```

#### **Application won't start**
```
Solutions:
1. Install Visual C++ Redistributable
2. Check Windows version compatibility (Windows 10+ required)
3. Run as administrator
4. Check antivirus software settings
```

#### **Missing DLL errors**
```
Solutions:
1. Use the installer version (includes all dependencies)
2. Install Visual C++ Redistributable manually
3. Reinstall the application
```

### 📱 **Android Issues**

#### **"App not installed" error**
```
Solutions:
1. Enable "Install from Unknown Sources"
2. Check available storage space (100MB+ required)
3. Try installing from file manager
4. Restart device and try again
```

#### **App crashes on startup**
```
Solutions:
1. Check Android version (7.0+ required)
2. Restart device
3. Clear app data and reinstall
4. Check device architecture compatibility
```

#### **Translation not working**
```
Solutions:
1. Check if C++ engine loaded (status indicator)
2. Try typing simple words first
3. Restart the application
4. JavaScript fallback should work automatically
```

### 🛠️ **Development Issues**

#### **Native module build fails**
```
Solutions:
1. Check Visual Studio installation (Windows)
2. Verify Python 3.8+ is installed
3. Check Android NDK installation
4. Run: npm run build-native -- --verbose
```

#### **Android build fails**
```
Solutions:
1. Check ANDROID_HOME environment variable
2. Verify Android SDK and NDK installation
3. Run: npx react-native doctor
4. Clean build: cd android && ./gradlew clean
```

#### **Metro bundler issues**
```
Solutions:
1. Clear Metro cache: npx react-native start --reset-cache
2. Delete node_modules and reinstall: rm -rf node_modules && npm install
3. Check port 8081 is not in use
```

---

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### 🎯 **Ways to Contribute**
- 🐛 **Bug Reports**: Report issues and bugs
- 💡 **Feature Requests**: Suggest new features
- 🔧 **Code Contributions**: Submit pull requests
- 📖 **Documentation**: Improve documentation
- 🌐 **Translations**: Add support for more languages
- 🧪 **Testing**: Test on different devices and platforms

### 📝 **Development Guidelines**

#### **Code Style**
- Use **TypeScript** for all new code
- Follow **existing code formatting**
- Add **comments** for complex logic
- Use **meaningful variable names**

#### **Commit Guidelines**
```bash
# Format: type(scope): description
feat(android): add new transliteration feature
fix(desktop): resolve clipboard issue
docs(readme): update installation instructions
style(ui): improve button styling
test(engine): add transliteration tests
```

#### **Pull Request Process**
1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Make** your changes with tests
4. **Update** documentation if needed
5. **Submit** a pull request
6. **Address** review feedback

### 🧪 **Testing**
```bash
# Run all tests
npm test

# Test on both platforms
npm run windows  # Test desktop
npm run android  # Test mobile

# Manual testing checklist
- Test transliteration accuracy
- Test keyboard shortcuts
- Test clipboard operations
- Test on different devices/OS versions
```

---

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### 📋 **License Summary**
- ✅ **Commercial use** allowed
- ✅ **Modification** allowed
- ✅ **Distribution** allowed
- ✅ **Private use** allowed
- ❌ **Liability** - No warranty provided
- ❌ **Warranty** - Software provided "as is"

---

## 🙏 Acknowledgments

### 🌟 **Special Thanks**
- **Tamil Language Community** - For feedback, testing, and cultural guidance
- **React Native Team** - For the amazing cross-platform framework
- **Electron Team** - For enabling desktop development with web technologies
- **SMC Project** - For Tamil transliteration research and algorithms
- **Open Source Contributors** - For libraries and tools that made this possible

### 🔧 **Built With**
- **[React Native](https://reactnative.dev/)** - Mobile app framework
- **[Electron](https://electronjs.org/)** - Desktop app framework
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[Node.js](https://nodejs.org/)** - JavaScript runtime
- **[Android NDK](https://developer.android.com/ndk)** - Native development kit
- **[CMake](https://cmake.org/)** - Cross-platform build system

### 📚 **Resources**
- **[Tamil Unicode Standard](https://unicode.org/charts/PDF/U0B80.pdf)** - Unicode specification for Tamil
- **[SMC Transliteration](https://smc.org.in/)** - Swathanthra Malayalam Computing
- **[React Native Documentation](https://reactnative.dev/docs/getting-started)** - Official React Native docs
- **[Electron Documentation](https://electronjs.org/docs)** - Official Electron docs

---

<div align="center">

**Made with ❤️ for the Tamil community**

[![GitHub stars](https://img.shields.io/github/stars/your-repo/tamil-transliterator?style=social)](https://github.com/your-repo/tamil-transliterator)
[![GitHub forks](https://img.shields.io/github/forks/your-repo/tamil-transliterator?style=social)](https://github.com/your-repo/tamil-transliterator/fork)
[![GitHub issues](https://img.shields.io/github/issues/your-repo/tamil-transliterator)](https://github.com/your-repo/tamil-transliterator/issues)

[⬆ Back to Top](#tamil-transliterator)

</div>
