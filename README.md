# Tamil Transliterator Desktop

A powerful Windows desktop application for Tamil transliteration with high-performance C++ engine.

![Tamil Transliterator](https://img.shields.io/badge/Platform-Windows-blue)
![Version](https://img.shields.io/badge/Version-1.0.0-green)
![License](https://img.shields.io/badge/License-MIT-yellow)

## 🎯 Overview

Tamil Transliterator Desktop is a professional-grade application that converts English text to Tamil script in real-time. Built with Electron and powered by a high-performance C++ transliteration engine, it provides fast, accurate, and reliable Tamil text conversion for Windows users.

## ✨ Features

### Core Functionality
- **Real-time Tamil Transliteration**: Type English and see Tamil instantly
- **Smart Suggestions**: Get alternative translations with context awareness
- **High Performance**: C++ engine for fast, accurate translations
- **50+ Word Dictionary**: Common Tamil words with perfect translations
- **Offline Operation**: No internet connection required

### User Interface
- **Modern Design**: Clean, professional interface
- **Dark/Light Themes**: Switch between themes for comfort
- **Real-time Feedback**: Character and word counting
- **Status Indicators**: Engine status and translation timing
- **Responsive Layout**: Adapts to different window sizes

### Desktop Features
- **Keyboard Shortcuts**: Full keyboard support for power users
- **Copy/Paste Integration**: Seamless clipboard operations
- **File Operations**: Save translations to text files
- **Menu System**: Standard Windows menus and shortcuts
- **Help System**: Built-in documentation and shortcuts guide

## 🎯 Supported Translations

### Common Words
```
vanakkam → வணக்கம் (Hello)
nandri → நன்றி (Thank you)
tamil → தமிழ் (Tamil)
amma → அம்மா (Mother)
appa → அப்பா (Father)
computer → கம்ப்யூட்டர் (Computer)
mobile → மொபைல் (Mobile)
```

### Character Transliteration
- **Vowels**: `a`, `aa`, `i`, `ii`, `u`, `uu`, `e`, `ee`, `ai`, `o`, `oo`, `au`
- **Consonants**: `k`, `g`, `ch`, `j`, `t`, `d`, `n`, `th`, `p`, `b`, `m`, `y`, `r`, `l`, `v`, `s`, `h`
- **Special**: `zh`, `ll`, `rr`, `nn`

## 💻 System Requirements

### Minimum Requirements
- **OS**: Windows 10 (64-bit) or Windows 11
- **RAM**: 4GB RAM
- **Storage**: 500MB free space
- **Processor**: Intel/AMD 64-bit processor

### Recommended
- **OS**: Windows 11
- **RAM**: 8GB RAM
- **Storage**: 1GB free space
- **Processor**: Modern Intel/AMD processor

## 🚀 Installation

### Option 1: Installer (Recommended)
1. Download `Tamil Transliterator Setup 1.0.0.exe` from the [release folder](./release/)
2. Run the installer as Administrator
3. Follow the installation wizard
4. Launch from Desktop shortcut or Start Menu

### Option 2: Portable Version
1. Download the `win-unpacked` folder from [release](./release/win-unpacked/)
2. Run `Tamil Transliterator.exe` from the folder
3. No installation required - perfect for USB drives

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+V` | Paste text into input |
| `Ctrl+A` | Select all input text |
| `Ctrl+C` | Copy Tamil output |
| `Ctrl+S` | Save output to file |
| `Ctrl+N` | Clear input (New) |
| `Escape` | Clear input or close help |
| `F1` | Toggle help panel |

## 🛠️ Development

### Prerequisites
- **Node.js 18+**: Download from [nodejs.org](https://nodejs.org/)
- **Python 3.7+**: Required for node-gyp
- **Visual Studio Build Tools**: For Windows C++ compilation

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd tamil-transliterator-desktop

# Install dependencies
npm install

# Build native C++ module
npm run build-native

# Start development mode
npm run dev
```

### Building Release
```bash
# Build for production
npm run build

# Output will be in release/ folder
```

## 📁 Project Structure

```
├── electron/                 # Electron application
│   ├── main.js              # Main process
│   ├── preload.js           # Preload script (security)
│   └── renderer/            # Frontend (HTML/CSS/JS)
├── native/                  # Node.js native module
│   ├── binding.gyp          # Build configuration
│   ├── package.json         # Native module config
│   └── src/                 # C++ wrapper code
├── cpp/                     # C++ transliteration engine
│   ├── SMCTransliterator_Complete.h
│   ├── SimpleTransliterator.h
│   ├── TranslateModule.h
│   └── TranslateModule.cpp
├── scripts/                 # Build scripts
│   └── build-native.js      # Native module builder
├── release/                 # Distribution files
│   ├── Tamil Transliterator Setup 1.0.0.exe
│   └── win-unpacked/
└── package.json             # Main package configuration
```

## 🔧 Architecture

### Layer 1: C++ Engine
- High-performance Tamil transliteration
- SMC Tamil transliterator with advanced features
- SimpleTransliterator fallback system
- 1000+ word dictionary with ML suggestions

### Layer 2: Node.js Native Bridge
- N-API wrapper for C++ functions
- Memory-safe interface
- Error handling and type conversion
- Performance optimization

### Layer 3: Electron Application
- Main process (Node.js backend)
- Renderer process (HTML/CSS/JS frontend)
- IPC communication for security
- Modern desktop UI with themes

### Layer 4: Distribution
- Electron Builder configuration
- Single .exe file generation
- No external dependencies required
- Windows installer creation

## 🔍 Troubleshooting

### App Won't Start
- **Solution**: Run as Administrator
- **Check**: Windows Defender isn't blocking the app
- **Verify**: You have Windows 10/11 64-bit

### Translation Not Working
- **Check**: Input text is in English
- **Try**: Common words like "vanakkam", "nandri"
- **Restart**: Close and reopen the application

### Performance Issues
- **Close**: Other heavy applications
- **Check**: Available RAM (4GB minimum)
- **Update**: Windows to latest version

### Windows Security Warnings
- **Why**: App isn't digitally signed (normal for new software)
- **Solution**: Click "More info" → "Run anyway"
- **Safe**: The application is safe to use

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **SMC Tamil Engine**: Based on SMC transliteration standards
- **Electron Framework**: For cross-platform desktop development
- **Node.js N-API**: For native module integration
- **Tamil Language Community**: For feedback and validation

## 📞 Support

- **Built-in Help**: Press F1 in the application
- **Documentation**: This README and inline help
- **Issues**: Report bugs or request features via GitHub issues

---

**Version**: 1.0.0
**Platform**: Windows 10/11 (64-bit)
**Size**: ~150-200MB
**Language**: English interface, Tamil output
