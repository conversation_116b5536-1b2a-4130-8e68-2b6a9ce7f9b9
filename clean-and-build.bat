@echo off
echo ========================================
echo Tamil Transliterator - Clean Build
echo ========================================
echo.

echo Fixing file lock issues and rebuilding...
echo.

echo [1/6] Stopping any running Electron processes...
taskkill /f /im "Tamil Transliterator.exe" 2>nul
taskkill /f /im "electron.exe" 2>nul
taskkill /f /im "app-builder.exe" 2>nul
echo Processes stopped (if any were running)

echo [2/6] Waiting for file locks to release...
timeout /t 3 /nobreak > nul

echo [3/6] Force removing dist folder...
if exist dist (
    echo Removing dist folder...
    rmdir /s /q dist 2>nul
    if exist dist (
        echo Some files still locked, trying alternative method...
        rd /s /q dist 2>nul
        if exist dist (
            echo Using PowerShell to force delete...
            powershell -Command "Remove-Item -Path 'dist' -Recurse -Force -ErrorAction SilentlyContinue"
        )
    )
)

echo [4/6] Cleaning node_modules cache...
if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache 2>nul
)

echo [5/6] Waiting before rebuild...
timeout /t 2 /nobreak > nul

echo [6/6] Building Electron application...
echo This may take several minutes...
echo.

call npm run build-electron-win
if errorlevel 1 (
    echo.
    echo ❌ BUILD FAILED
    echo.
    echo Common solutions:
    echo 1. Close any running Tamil Transliterator windows
    echo 2. Close VS Code or other editors that might have files open
    echo 3. Restart your computer to clear all file locks
    echo 4. Run this script as Administrator
    echo 5. Temporarily disable antivirus
    echo.
    echo Try running this script again after closing all applications.
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ BUILD SUCCESSFUL!
echo ========================================
echo.

if exist "dist\Tamil Transliterator Setup 1.0.0.exe" (
    echo ✓ Installer created: dist\Tamil Transliterator Setup 1.0.0.exe
    for %%A in ("dist\Tamil Transliterator Setup 1.0.0.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo   Size: !sizeMB! MB
    )
)

if exist "dist\win-unpacked\Tamil Transliterator.exe" (
    echo ✓ Portable version: dist\win-unpacked\Tamil Transliterator.exe
)

echo.
echo 📦 Distribution files ready!
echo.
echo 🚀 Next steps:
echo 1. Test the installer on this machine
echo 2. Test on another Windows computer
echo 3. Share with users!
echo.

echo Opening dist folder...
start explorer "dist"

echo.
echo 💡 Tips for users:
echo - If Windows Defender warns, click "More info" → "Run anyway"
echo - This is normal for new applications
echo - The app is safe to use
echo.

pause
