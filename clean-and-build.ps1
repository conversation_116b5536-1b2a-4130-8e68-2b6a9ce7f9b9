# Tamil Transliterator - Clean Build (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Tamil Transliterator - Clean Build" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Fixing file lock issues and rebuilding..." -ForegroundColor Yellow
Write-Host ""

# Stop any running processes
Write-Host "[1/6] Stopping any running Electron processes..." -ForegroundColor Green
Get-Process -Name "Tamil Transliterator" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "app-builder" -ErrorAction SilentlyContinue | Stop-Process -Force
Write-Host "Processes stopped (if any were running)"

# Wait for file locks to release
Write-Host "[2/6] Waiting for file locks to release..." -ForegroundColor Green
Start-Sleep -Seconds 3

# Force remove dist folder
Write-Host "[3/6] Force removing dist folder..." -ForegroundColor Green
if (Test-Path "dist") {
    Write-Host "Removing dist folder..."
    try {
        Remove-Item -Path "dist" -Recurse -Force -ErrorAction Stop
        Write-Host "✓ Dist folder removed successfully"
    }
    catch {
        Write-Host "⚠️ Some files were locked, trying alternative method..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
        try {
            Get-ChildItem -Path "dist" -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
            Remove-Item -Path "dist" -Force -ErrorAction SilentlyContinue
        }
        catch {
            Write-Host "⚠️ Could not remove all files. You may need to restart your computer." -ForegroundColor Yellow
        }
    }
}

# Clean cache
Write-Host "[4/6] Cleaning node_modules cache..." -ForegroundColor Green
if (Test-Path "node_modules\.cache") {
    Remove-Item -Path "node_modules\.cache" -Recurse -Force -ErrorAction SilentlyContinue
}

# Wait before rebuild
Write-Host "[5/6] Waiting before rebuild..." -ForegroundColor Green
Start-Sleep -Seconds 2

# Build
Write-Host "[6/6] Building Electron application..." -ForegroundColor Green
Write-Host "This may take several minutes..." -ForegroundColor Yellow
Write-Host ""

$buildResult = & npm run build-electron-win
$exitCode = $LASTEXITCODE

if ($exitCode -ne 0) {
    Write-Host ""
    Write-Host "❌ BUILD FAILED" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Close any running Tamil Transliterator windows"
    Write-Host "2. Close VS Code or other editors that might have files open"
    Write-Host "3. Restart your computer to clear all file locks"
    Write-Host "4. Run PowerShell as Administrator"
    Write-Host "5. Temporarily disable antivirus"
    Write-Host ""
    Write-Host "Try running this script again after closing all applications."
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "✅ BUILD SUCCESSFUL!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check output files
if (Test-Path "dist\Tamil Transliterator Setup 1.0.0.exe") {
    $size = (Get-Item "dist\Tamil Transliterator Setup 1.0.0.exe").Length
    $sizeMB = [math]::Round($size / 1MB, 1)
    Write-Host "✓ Installer created: dist\Tamil Transliterator Setup 1.0.0.exe" -ForegroundColor Green
    Write-Host "  Size: $sizeMB MB"
}

if (Test-Path "dist\win-unpacked\Tamil Transliterator.exe") {
    Write-Host "✓ Portable version: dist\win-unpacked\Tamil Transliterator.exe" -ForegroundColor Green
}

Write-Host ""
Write-Host "📦 Distribution files ready!" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Yellow
Write-Host "1. Test the installer on this machine"
Write-Host "2. Test on another Windows computer"
Write-Host "3. Share with users!"
Write-Host ""

# Open dist folder
Write-Host "Opening dist folder..." -ForegroundColor Green
Start-Process explorer "dist"

Write-Host ""
Write-Host "💡 Tips for users:" -ForegroundColor Yellow
Write-Host "- If Windows Defender warns, click 'More info' → 'Run anyway'"
Write-Host "- This is normal for new applications"
Write-Host "- The app is safe to use"
Write-Host ""

Read-Host "Press Enter to exit"
