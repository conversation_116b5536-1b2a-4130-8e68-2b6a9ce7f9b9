# Tamil Transliterator - Release Files

This folder contains the built applications for both Windows and Android platforms.

## 📁 Folder Structure

```
release/
├── windows/                           # Windows Desktop Builds
│   ├── Tamil Transliterator Setup 1.0.0.exe  # Windows Installer
│   └── win-unpacked/                  # Portable Version
│       ├── Tamil Transliterator.exe   # Main executable
│       ├── resources/                 # Application resources
│       └── [other files]              # Dependencies and libraries
└── android/                           # Android Mobile Builds
    └── [APK files will be placed here]
```

## 🖥️ Windows Desktop

### Installer Version
- **File**: `Tamil Transliterator Setup 1.0.0.exe`
- **Size**: ~150MB
- **Type**: NSIS Installer
- **Features**: 
  - Automatic installation
  - Start Menu shortcuts
  - Desktop shortcut
  - Uninstaller included
  - Windows integration

### Portable Version
- **Folder**: `win-unpacked/`
- **Size**: ~200MB
- **Type**: Portable application
- **Features**:
  - No installation required
  - Run from any location
  - Includes all dependencies
  - Perfect for USB drives

## 📱 Android Mobile

### APK Installation
- **File**: `[To be generated]`
- **Size**: ~50MB
- **Type**: Android Package
- **Features**:
  - Direct installation
  - No Google Play required
  - Works on Android 7.0+
  - All architectures supported

## 🚀 Installation Instructions

### Windows Desktop

#### Using Installer (Recommended)
1. Download `Tamil Transliterator Setup 1.0.0.exe`
2. Right-click and select "Run as administrator" (if needed)
3. Follow the installation wizard
4. Launch from Start Menu or Desktop

#### Using Portable Version
1. Download and extract the `win-unpacked` folder
2. Navigate to the extracted folder
3. Double-click `Tamil Transliterator.exe`
4. The application will start immediately

### Android Mobile

#### APK Installation
1. Download the APK file
2. Enable "Install from Unknown Sources":
   - Go to Settings > Security
   - Enable "Unknown Sources" or "Install unknown apps"
3. Open the APK file and install
4. Launch from app drawer

## 🔧 System Requirements

### Windows Desktop
- **OS**: Windows 10 (64-bit) or Windows 11
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space
- **Processor**: Intel/AMD 64-bit processor
- **Additional**: Visual C++ Redistributable (included in installer)

### Android Mobile
- **OS**: Android 7.0 (API level 24) or later
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 100MB free space
- **Architecture**: ARM64, ARMv7, x86, x86_64

## 🛡️ Security Information

### Windows Desktop
- **Code Signing**: Applications are not code-signed (development build)
- **Windows Defender**: May show warning for unsigned applications
- **Safe to Install**: All files are clean and safe
- **Antivirus**: Some antivirus software may flag unsigned executables

### Android Mobile
- **APK Signing**: Debug-signed APK (development build)
- **Permissions**: Minimal permissions required
- **Safe to Install**: No malicious code or tracking
- **Google Play Protect**: May show warning for unknown developer

## 📊 Version Information

### Current Release: v1.0.0
- **Build Date**: [Current Date]
- **Platform**: Windows 64-bit, Android ARM64/ARMv7/x86/x86_64
- **Engine**: C++ transliteration engine
- **UI Framework**: Electron (Windows), React Native (Android)

### Features Included
- ✅ Real-time Tamil transliteration
- ✅ Smart suggestions
- ✅ Clipboard integration
- ✅ Keyboard shortcuts
- ✅ Dark/Light themes
- ✅ Offline functionality
- ✅ High-performance C++ engine

## 🔄 Update Instructions

### Windows Desktop
1. Download the latest installer
2. Run the new installer (will update existing installation)
3. Or use portable version for side-by-side installation

### Android Mobile
1. Download the latest APK
2. Install over existing version (data will be preserved)
3. Or uninstall old version first for clean installation

## 🐛 Troubleshooting

### Windows Desktop

#### "Windows protected your PC" message
1. Click "More info"
2. Click "Run anyway"
3. This is normal for unsigned applications

#### Application won't start
1. Install Visual C++ Redistributable
2. Check Windows version compatibility
3. Run as administrator

#### Missing DLL errors
1. Use the installer version (includes all dependencies)
2. Install Visual C++ Redistributable manually

### Android Mobile

#### "App not installed" error
1. Enable "Install from Unknown Sources"
2. Check available storage space
3. Try installing from file manager

#### App crashes on startup
1. Check Android version (7.0+ required)
2. Restart device
3. Clear app data and reinstall

## 📞 Support

For technical support:
1. Check the main README.md for documentation
2. Review troubleshooting section above
3. Create an issue on the project repository
4. Contact the development team

## 📄 License

All release files are distributed under the MIT License. See the main LICENSE file for details.

---

**Tamil Transliterator v1.0.0**  
**Multi-Platform Tamil Transliteration Application**  
**Made with ❤️ for the Tamil community**
